import type { ApiResponse, ApiError } from '../types';

/**
 * 生成唯一请求ID
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  requestId: string,
  statusCode: number = 200
): Response {
  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    requestId
  };

  return new Response(JSON.stringify(response), {
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      'X-Request-ID': requestId
    }
  });
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  error: ApiError,
  requestId: string
): Response {
  const response: ApiResponse = {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      details: error.details
    },
    timestamp: new Date().toISOString(),
    requestId
  };

  return new Response(JSON.stringify(response), {
    status: error.statusCode,
    headers: {
      'Content-Type': 'application/json',
      'X-Request-ID': requestId
    }
  });
}

/**
 * 创建标准错误对象
 */
export function createApiError(
  code: string,
  message: string,
  statusCode: number = 500,
  details?: any
): ApiError {
  return {
    code,
    message,
    statusCode,
    details
  };
}

/**
 * 常用错误创建函数
 */
export const ApiErrors = {
  badRequest: (message: string, details?: any) =>
    createApiError('BAD_REQUEST', message, 400, details),
  
  unauthorized: (message: string = 'Unauthorized') =>
    createApiError('UNAUTHORIZED', message, 401),
  
  forbidden: (message: string = 'Forbidden') =>
    createApiError('FORBIDDEN', message, 403),
  
  notFound: (message: string = 'Not found') =>
    createApiError('NOT_FOUND', message, 404),
  
  methodNotAllowed: (message: string = 'Method not allowed') =>
    createApiError('METHOD_NOT_ALLOWED', message, 405),
  
  tooManyRequests: (message: string = 'Too many requests') =>
    createApiError('TOO_MANY_REQUESTS', message, 429),
  
  internalError: (message: string = 'Internal server error', details?: any) =>
    createApiError('INTERNAL_ERROR', message, 500, details),
  
  serviceUnavailable: (message: string = 'Service unavailable') =>
    createApiError('SERVICE_UNAVAILABLE', message, 503)
};
