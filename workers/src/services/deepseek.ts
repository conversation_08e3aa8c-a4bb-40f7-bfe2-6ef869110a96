import type {
  En<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ElementProfile,
  DestinyAnalysis,
  ReadingContent,
  CrystalRecommendation
} from '../types';
import { ConfigService } from './config';
import { PromptService } from './prompt';

/**
 * DeepSeek API 响应类型
 */
interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

interface DeepSeekError {
  error: {
    message: string;
    type: string;
  };
}

/**
 * DeepSeek AI 服务
 * 提供八字分析、命理解读、水晶推荐等 AI 功能
 */
export class DeepSeekService {
  private configService: ConfigService;
  private promptService: PromptService;
  private config: {
    apiKey: string;
    baseUrl: string;
    maxTokens: number;
    temperature: number;
  } | null = null;

  constructor(env: Env, promptService: PromptService) {
    this.configService = new ConfigService(env);
    this.promptService = promptService;
  }

  /**
   * 初始化配置
   */
  private async initConfig(): Promise<void> {
    if (this.config) return;

    const deepseekConfig = await this.configService.getDeepSeekConfig();
    const maxTokens = await this.configService.getConfig('DEEPSEEK_MAX_TOKENS');
    const temperature = await this.configService.getConfig('DEEPSEEK_TEMPERATURE');

    this.config = {
      apiKey: deepseekConfig.apiKey,
      baseUrl: deepseekConfig.baseUrl,
      maxTokens: parseInt(maxTokens || '4000'), // 增加到4000以支持更详细的内容
      temperature: parseFloat(temperature || '0.7')
    };
  }

  /**
   * 发送请求到 DeepSeek API
   */
  private async makeRequest(systemPrompt: string, userPrompt: string): Promise<string> {
    await this.initConfig();

    if (!this.config) {
      throw new Error('DeepSeek configuration not available');
    }

    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      })
    });

    if (!response.ok) {
      const errorData: DeepSeekError = await response.json();
      throw new Error(`DeepSeek API Error: ${errorData.error.message}`);
    }

    const data: DeepSeekResponse = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  /**
   * 分析命运
   */
  async analyzeDestiny(chart: BaZiChart, elementProfile: ElementProfile): Promise<DestinyAnalysis> {
    try {
      console.log('[DeepSeekService] Starting destiny analysis...');

      // 获取命理分析 prompt
      const prompt = await this.promptService.getPrompt('destiny_analysis', {
        year_pillar: `${chart.year.heavenly}${chart.year.earthly}`,
        month_pillar: `${chart.month.heavenly}${chart.month.earthly}`,
        day_pillar: `${chart.day.heavenly}${chart.day.earthly}`,
        hour_pillar: `${chart.hour.heavenly}${chart.hour.earthly}`,
        year_element: chart.year.element,
        month_element: chart.month.element,
        day_element: chart.day.element,
        hour_element: chart.hour.element,
        wood_percent: elementProfile.wood,
        fire_percent: elementProfile.fire,
        earth_percent: elementProfile.earth,
        metal_percent: elementProfile.metal,
        water_percent: elementProfile.water
      });

      console.log('[DeepSeekService] Prompt retrieved, making API request...');
      const response = await this.makeRequest(prompt.systemPrompt, prompt.userPrompt);
      
      // 尝试解析JSON响应
      try {
        const analysis = JSON.parse(response);
        return {
          overall: analysis.overall || '整体分析暂不可用',
          elements: analysis.elements || '五行分析暂不可用',
          personality: analysis.personality || '性格分析暂不可用',
          strengths: analysis.strengths || '优势分析暂不可用',
          challenges: analysis.challenges || '挑战分析暂不可用',
          advice: analysis.advice || '建议暂不可用'
        };
      } catch (parseError) {
        // 如果JSON解析失败，将整个响应作为overall内容
        return {
          overall: response,
          elements: '五行分析暂不可用',
          personality: '性格分析暂不可用',
          strengths: '优势分析暂不可用',
          challenges: '挑战分析暂不可用',
          advice: '建议暂不可用'
        };
      }
    } catch (error) {
      console.error('DeepSeek API analysis error:', error);
      throw new Error('命理分析服务暂时不可用，请稍后重试');
    }
  }

  /**
   * 生成详细解读
   */
  async generateReadings(analysis: DestinyAnalysis): Promise<ReadingContent> {
    try {
      console.log('[DeepSeekService] Starting detailed readings generation...');

      // 获取详细解读 prompt
      const prompt = await this.promptService.getPrompt('detailed_readings', {
        base_analysis: JSON.stringify(analysis)
      });

      console.log('[DeepSeekService] Prompt retrieved, making API request...');
      const response = await this.makeRequest(prompt.systemPrompt, prompt.userPrompt);

      try {
        const readings = JSON.parse(response);
        return {
          career: readings.career || '事业运势解读暂不可用',
          relationships: readings.relationships || '感情运势解读暂不可用',
          health: readings.health || '健康运势解读暂不可用',
          wealth: readings.wealth || '财运分析暂不可用',
          development: readings.development || '发展建议暂不可用'
        };
      } catch (parseError) {
        return {
          career: response,
          relationships: '感情运势解读暂不可用',
          health: '健康运势解读暂不可用',
          wealth: '财运分析暂不可用',
          development: '发展建议暂不可用'
        };
      }
    } catch (error) {
      console.error('DeepSeek API readings error:', error);
      throw new Error('详细解读服务暂时不可用，请稍后重试');
    }
  }

  /**
   * 生成水晶推荐
   */
  async generateCrystalRecommendations(elementProfile: ElementProfile): Promise<CrystalRecommendation[]> {
    try {
      console.log('[DeepSeekService] Starting crystal recommendations generation...');

      // 获取水晶推荐 prompt
      const prompt = await this.promptService.getPrompt('crystal_recommendations', {
        wood_percent: elementProfile.wood,
        fire_percent: elementProfile.fire,
        earth_percent: elementProfile.earth,
        metal_percent: elementProfile.metal,
        water_percent: elementProfile.water,
        wood_status: this.getElementStatus(elementProfile.wood),
        fire_status: this.getElementStatus(elementProfile.fire),
        earth_status: this.getElementStatus(elementProfile.earth),
        metal_status: this.getElementStatus(elementProfile.metal),
        water_status: this.getElementStatus(elementProfile.water)
      });

      console.log('[DeepSeekService] Prompt retrieved, making API request...');
      const response = await this.makeRequest(prompt.systemPrompt, prompt.userPrompt);
      
      try {
        const recommendations = JSON.parse(response);
        return Array.isArray(recommendations) ? recommendations : [];
      } catch (parseError) {
        // 如果解析失败，返回默认推荐
        return [
          {
            name: 'Clear Quartz',
            nameZh: '白水晶',
            element: 'Universal',
            benefit: 'Amplifies energy and brings clarity',
            benefitZh: '增强能量，带来清晰思维',
            reason: '适合平衡各种五行元素'
          }
        ];
      }
    } catch (error) {
      console.error('DeepSeek API crystal recommendations error:', error);
      // 返回默认推荐
      return [
        {
          name: 'Clear Quartz',
          nameZh: '白水晶',
          element: 'Universal',
          benefit: 'Amplifies energy and brings clarity',
          benefitZh: '增强能量，带来清晰思维',
          reason: '适合平衡各种五行元素'
        }
      ];
    }
  }

  /**
   * 获取五行状态描述
   * @param percentage 五行百分比
   * @returns 状态描述
   */
  private getElementStatus(percentage: number): string {
    if (percentage >= 30) return '偏旺';
    if (percentage >= 20) return '平衡';
    if (percentage >= 10) return '偏弱';
    return '极弱';
  }
}
