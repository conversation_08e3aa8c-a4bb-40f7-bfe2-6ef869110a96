import type { BirthInfo, BaZiChart, ElementProfile } from '../types';

/**
 * 天干
 */
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

/**
 * 地支
 */
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

/**
 * 天干对应五行
 */
const HEAVENLY_STEM_ELEMENTS: Record<string, string> = {
  '甲': 'wood', '乙': 'wood',
  '丙': 'fire', '丁': 'fire',
  '戊': 'earth', '己': 'earth',
  '庚': 'metal', '辛': 'metal',
  '壬': 'water', '癸': 'water'
};

/**
 * 地支对应五行
 */
const EARTHLY_BRANCH_ELEMENTS: Record<string, string> = {
  '子': 'water', '丑': 'earth', '寅': 'wood', '卯': 'wood',
  '辰': 'earth', '巳': 'fire', '午': 'fire', '未': 'earth',
  '申': 'metal', '酉': 'metal', '戌': 'earth', '亥': 'water'
};

/**
 * 时辰对应地支
 */
const HOUR_TO_EARTHLY_BRANCH: Record<number, string> = {
  23: '子', 0: '子', 1: '丑', 2: '丑', 3: '寅', 4: '寅',
  5: '卯', 6: '卯', 7: '辰', 8: '辰', 9: '巳', 10: '巳',
  11: '午', 12: '午', 13: '未', 14: '未', 15: '申', 16: '申',
  17: '酉', 18: '酉', 19: '戌', 20: '戌', 21: '亥', 22: '亥'
};

/**
 * 八字计算服务
 */
export class BaZiCalculator {
  /**
   * 计算某一年的天干地支
   */
  private calculateYearPillar(year: number): { heavenly: string; earthly: string } {
    // 公历年份转换为干支纪年
    // 天干循环：(year - 4) % 10
    // 地支循环：(year - 4) % 12
    const heavenlyIndex = (year - 4) % 10;
    const earthlyIndex = (year - 4) % 12;
    
    return {
      heavenly: HEAVENLY_STEMS[heavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex]
    };
  }

  /**
   * 计算某一月的天干地支
   */
  private calculateMonthPillar(year: number, month: number): { heavenly: string; earthly: string } {
    // 月支固定：正月寅，二月卯，三月辰...
    const earthlyIndex = (month + 1) % 12; // 正月为寅（索引2）
    const earthly = EARTHLY_BRANCHES[earthlyIndex];
    
    // 月干计算：年干序数 * 2 + 月支序数
    const yearHeavenlyIndex = (year - 4) % 10;
    const heavenlyIndex = (yearHeavenlyIndex * 2 + earthlyIndex) % 10;
    
    return {
      heavenly: HEAVENLY_STEMS[heavenlyIndex],
      earthly
    };
  }

  /**
   * 计算某一日的天干地支
   */
  private calculateDayPillar(date: Date): { heavenly: string; earthly: string } {
    // 简化版日干支计算（实际应该使用更复杂的万年历算法）
    // 这里使用一个基准日期进行计算
    const baseDate = new Date(1900, 0, 1); // 1900年1月1日
    const baseDayIndex = 0; // 假设基准日为甲子日
    
    const timeDiff = date.getTime() - baseDate.getTime();
    const dayDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    const heavenlyIndex = (baseDayIndex + dayDiff) % 10;
    const earthlyIndex = (baseDayIndex + dayDiff) % 12;
    
    return {
      heavenly: HEAVENLY_STEMS[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex]
    };
  }

  /**
   * 计算某一时的天干地支
   */
  private calculateHourPillar(date: Date, dayHeavenly: string): { heavenly: string; earthly: string } {
    const hour = date.getHours();
    
    // 根据时辰确定地支
    let earthlyIndex = 0;
    for (const [h, branch] of Object.entries(HOUR_TO_EARTHLY_BRANCH)) {
      if (hour === parseInt(h)) {
        earthlyIndex = EARTHLY_BRANCHES.indexOf(branch);
        break;
      }
    }
    
    // 时干计算公式：日干序数 * 2 + 时支序数
    const dayHeavenlyIndex = HEAVENLY_STEMS.indexOf(dayHeavenly);
    const hourHeavenlyIndex = (dayHeavenlyIndex * 2 + earthlyIndex) % 10;
    
    return {
      heavenly: HEAVENLY_STEMS[hourHeavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex]
    };
  }

  /**
   * 获取干支对应的五行
   */
  private getElement(heavenly: string, earthly: string): string {
    // 以天干五行为主
    return HEAVENLY_STEM_ELEMENTS[heavenly] || 'unknown';
  }

  /**
   * 主要计算方法
   */
  calculateBaZi(birthInfo: BirthInfo): BaZiChart {
    const birthDate = new Date(birthInfo.date + 'T' + birthInfo.time);
    const year = birthDate.getFullYear();
    const month = birthDate.getMonth() + 1; // JavaScript月份从0开始
    
    const yearPillar = this.calculateYearPillar(year);
    const monthPillar = this.calculateMonthPillar(year, month);
    const dayPillar = this.calculateDayPillar(birthDate);
    const hourPillar = this.calculateHourPillar(birthDate, dayPillar.heavenly);

    return {
      year: {
        heavenly: yearPillar.heavenly,
        earthly: yearPillar.earthly,
        element: this.getElement(yearPillar.heavenly, yearPillar.earthly)
      },
      month: {
        heavenly: monthPillar.heavenly,
        earthly: monthPillar.earthly,
        element: this.getElement(monthPillar.heavenly, monthPillar.earthly)
      },
      day: {
        heavenly: dayPillar.heavenly,
        earthly: dayPillar.earthly,
        element: this.getElement(dayPillar.heavenly, dayPillar.earthly)
      },
      hour: {
        heavenly: hourPillar.heavenly,
        earthly: hourPillar.earthly,
        element: this.getElement(hourPillar.heavenly, hourPillar.earthly)
      }
    };
  }

  /**
   * 计算五行分布
   */
  calculateElementProfile(chart: BaZiChart): ElementProfile {
    const elements = {
      wood: 0,
      fire: 0,
      earth: 0,
      metal: 0,
      water: 0
    };

    // 统计天干五行
    elements[chart.year.element as keyof typeof elements] += 1;
    elements[chart.month.element as keyof typeof elements] += 1;
    elements[chart.day.element as keyof typeof elements] += 1;
    elements[chart.hour.element as keyof typeof elements] += 1;

    // 统计地支五行
    elements[EARTHLY_BRANCH_ELEMENTS[chart.year.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.month.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.day.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.hour.earthly] as keyof typeof elements] += 1;

    // 转换为百分比
    const total = Object.values(elements).reduce((sum, count) => sum + count, 0);
    
    return {
      wood: Math.round((elements.wood / total) * 100),
      fire: Math.round((elements.fire / total) * 100),
      earth: Math.round((elements.earth / total) * 100),
      metal: Math.round((elements.metal / total) * 100),
      water: Math.round((elements.water / total) * 100)
    };
  }

  /**
   * 获取五行平衡建议
   */
  getElementBalance(elementProfile: ElementProfile): {
    dominant: string[];
    lacking: string[];
    balanced: boolean;
  } {
    const elements = Object.entries(elementProfile);
    const average = 20; // 理想情况下每个元素占20%
    const threshold = 10; // 偏差阈值

    const dominant = elements
      .filter(([_, percentage]) => percentage > average + threshold)
      .map(([element, _]) => element);

    const lacking = elements
      .filter(([_, percentage]) => percentage < average - threshold)
      .map(([element, _]) => element);

    const balanced = dominant.length === 0 && lacking.length === 0;

    return { dominant, lacking, balanced };
  }
}
