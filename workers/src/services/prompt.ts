import { SupabaseClient } from '@supabase/supabase-js';

// AI Prompt 接口定义
export interface AIPrompt {
  id: string;
  name: string;
  title: string;
  system_prompt: string;
  user_prompt_template: string;
  variables: Record<string, any>;
  is_active: boolean;
}

// 处理后的 Prompt 接口
export interface ProcessedPrompt {
  systemPrompt: string;
  userPrompt: string;
}

// 缓存项接口
interface CachedPrompt {
  prompt: AIPrompt;
  timestamp: number;
}

/**
 * Prompt 服务类 - 简化版
 * 负责从数据库获取 AI prompt 并处理模板变量
 */
export class PromptService {
  private supabase: SupabaseClient;
  private cache: Map<string, CachedPrompt> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * 获取并处理 prompt
   * @param name prompt 名称
   * @param variables 模板变量
   * @returns 处理后的 prompt
   */
  async getPrompt(name: string, variables?: Record<string, any>): Promise<ProcessedPrompt> {
    try {
      // 1. 检查缓存
      const cached = this.getCachedPrompt(name);
      if (cached && !this.isCacheExpired(cached)) {
        console.log(`[PromptService] Cache hit for prompt: ${name}`);
        return this.processPrompt(cached.prompt, variables);
      }

      // 2. 从数据库获取
      console.log(`[PromptService] Fetching prompt from database: ${name}`);
      const prompt = await this.fetchPrompt(name);
      if (!prompt) {
        throw new Error(`Prompt not found: ${name}`);
      }

      // 3. 缓存并返回
      this.cachePrompt(name, prompt);
      return this.processPrompt(prompt, variables);

    } catch (error) {
      console.error(`[PromptService] Error getting prompt ${name}:`, error);
      throw error;
    }
  }

  /**
   * 处理 prompt 模板变量
   * @param prompt 原始 prompt
   * @param variables 变量值
   * @returns 处理后的 prompt
   */
  private processPrompt(prompt: AIPrompt, variables?: Record<string, any>): ProcessedPrompt {
    let userPrompt = prompt.user_prompt_template;
    
    // 替换模板变量
    if (variables) {
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        const regex = new RegExp(placeholder, 'g');
        userPrompt = userPrompt.replace(regex, String(value));
      });
    }

    return {
      systemPrompt: prompt.system_prompt,
      userPrompt
    };
  }

  /**
   * 从数据库获取 prompt
   * @param name prompt 名称
   * @returns prompt 数据或 null
   */
  private async fetchPrompt(name: string): Promise<AIPrompt | null> {
    try {
      const { data, error } = await this.supabase
        .from('ai_prompts')
        .select('*')
        .eq('name', name)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error(`[PromptService] Database error fetching prompt ${name}:`, error);
        return null;
      }

      return data as AIPrompt;

    } catch (error) {
      console.error(`[PromptService] Exception fetching prompt ${name}:`, error);
      return null;
    }
  }

  /**
   * 获取缓存的 prompt
   * @param name prompt 名称
   * @returns 缓存项或 null
   */
  private getCachedPrompt(name: string): CachedPrompt | null {
    return this.cache.get(name) || null;
  }

  /**
   * 检查缓存是否过期
   * @param cached 缓存项
   * @returns 是否过期
   */
  private isCacheExpired(cached: CachedPrompt): boolean {
    return Date.now() - cached.timestamp > this.cacheExpiry;
  }

  /**
   * 缓存 prompt
   * @param name prompt 名称
   * @param prompt prompt 数据
   */
  private cachePrompt(name: string, prompt: AIPrompt): void {
    this.cache.set(name, {
      prompt,
      timestamp: Date.now()
    });
    console.log(`[PromptService] Cached prompt: ${name}`);
  }

  /**
   * 清除缓存
   * @param name 可选的 prompt 名称，不提供则清除所有缓存
   */
  clearCache(name?: string): void {
    if (name) {
      this.cache.delete(name);
      console.log(`[PromptService] Cleared cache for: ${name}`);
    } else {
      this.cache.clear();
      console.log(`[PromptService] Cleared all cache`);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
