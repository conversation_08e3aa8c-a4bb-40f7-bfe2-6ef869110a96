import { createClient } from '@supabase/supabase-js';
import type { Env, AppConfig } from '../types';

/**
 * 配置服务 - 从数据库读取所有配置
 * 按照要求，所有环境变量都从数据库读取，不做文件硬编码
 */
export class ConfigService {
  private supabase: any;
  private configCache: Map<string, string> = new Map();
  private cacheExpiry: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  constructor(private env: Env) {
    // 初始化 Supabase 客户端
    const supabaseUrl = env.SUPABASE_URL;
    const supabaseKey = env.SUPABASE_SERVICE_KEY;

    if (!supabaseUrl) {
      console.warn('Supabase URL not configured, using fallback configuration');
      return;
    }

    if (!supabaseKey) {
      console.warn('Supabase service key not configured, using fallback configuration');
      return;
    }

    try {
      this.supabase = createClient(supabaseUrl, supabaseKey);
    } catch (error) {
      console.error('Failed to initialize Supabase client:', error);
    }
  }

  /**
   * 从数据库获取配置值
   */
  async getConfig(key: string): Promise<string | null> {
    // 检查缓存
    if (this.isCacheValid() && this.configCache.has(key)) {
      return this.configCache.get(key) || null;
    }

    // 如果没有 Supabase 客户端，使用环境变量 fallback
    if (!this.supabase) {
      return this.getFallbackConfig(key);
    }

    try {
      const { data, error } = await this.supabase
        .from('app_configs')
        .select('value')
        .eq('key', key)
        .single();

      if (error) {
        console.error(`Failed to get config ${key}:`, error);
        return this.getFallbackConfig(key);
      }

      // 更新缓存
      if (data?.value) {
        this.configCache.set(key, data.value);
        this.cacheExpiry = Date.now() + this.CACHE_TTL;
      }

      return data?.value || null;
    } catch (error) {
      console.error(`Error getting config ${key}:`, error);
      return this.getFallbackConfig(key);
    }
  }

  /**
   * 批量获取配置
   */
  async getAllConfigs(): Promise<Record<string, string>> {
    // 检查缓存
    if (this.isCacheValid() && this.configCache.size > 0) {
      return Object.fromEntries(this.configCache);
    }

    // 如果没有 Supabase 客户端，使用环境变量 fallback
    if (!this.supabase) {
      return this.getAllFallbackConfigs();
    }

    try {
      const { data, error } = await this.supabase
        .from('app_configs')
        .select('key, value');

      if (error) {
        console.error('Failed to get all configs:', error);
        return this.getAllFallbackConfigs();
      }

      // 更新缓存
      this.configCache.clear();
      const configs: Record<string, string> = {};

      data?.forEach((config: AppConfig) => {
        this.configCache.set(config.key, config.value);
        configs[config.key] = config.value;
      });

      this.cacheExpiry = Date.now() + this.CACHE_TTL;
      return configs;
    } catch (error) {
      console.error('Error getting all configs:', error);
      return this.getAllFallbackConfigs();
    }
  }

  /**
   * 获取 DeepSeek API 配置
   */
  async getDeepSeekConfig() {
    const [apiKey, baseUrl] = await Promise.all([
      this.getConfig('DEEPSEEK_API_KEY'),
      this.getConfig('DEEPSEEK_BASE_URL')
    ]);

    return {
      apiKey: apiKey || this.env.DEEPSEEK_API_KEY || '***********************************',
      baseUrl: baseUrl || this.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com'
    };
  }

  /**
   * 获取 Supabase 配置
   */
  async getSupabaseConfig() {
    const [url, serviceKey] = await Promise.all([
      this.getConfig('SUPABASE_URL'),
      this.getConfig('SUPABASE_SERVICE_KEY')
    ]);

    return {
      url: url || this.env.SUPABASE_URL,
      serviceKey: serviceKey || this.env.SUPABASE_SERVICE_KEY
    };
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return Date.now() < this.cacheExpiry;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.configCache.clear();
    this.cacheExpiry = 0;
  }

  /**
   * 获取 fallback 配置（用于本地开发）
   */
  private getFallbackConfig(key: string): string | null {
    const fallbackConfigs: Record<string, string> = {
      'DEEPSEEK_API_KEY': this.env.DEEPSEEK_API_KEY || '***********************************',
      'DEEPSEEK_BASE_URL': this.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
      'DEEPSEEK_MAX_TOKENS': '2000',
      'DEEPSEEK_TEMPERATURE': '0.7',
      'API_RATE_LIMIT_PER_MINUTE': '60',
      'API_RATE_LIMIT_PER_HOUR': '1000',
      'CACHE_TTL_SECONDS': '300',
      'READING_COST_CREDITS': '10',
      'PREMIUM_CONTENT_COST_CREDITS': '5'
    };

    return fallbackConfigs[key] || null;
  }

  /**
   * 获取所有 fallback 配置
   */
  private getAllFallbackConfigs(): Record<string, string> {
    return {
      'DEEPSEEK_API_KEY': this.env.DEEPSEEK_API_KEY || '***********************************',
      'DEEPSEEK_BASE_URL': this.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
      'DEEPSEEK_MAX_TOKENS': '2000',
      'DEEPSEEK_TEMPERATURE': '0.7',
      'API_RATE_LIMIT_PER_MINUTE': '60',
      'API_RATE_LIMIT_PER_HOUR': '1000',
      'CACHE_TTL_SECONDS': '300',
      'READING_COST_CREDITS': '10',
      'PREMIUM_CONTENT_COST_CREDITS': '5'
    };
  }
}
