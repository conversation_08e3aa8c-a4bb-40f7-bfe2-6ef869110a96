import { Hono } from 'hono';
import { z } from 'zod';
import type { Env, Variables } from '../types';
import { SupabaseService } from '../services/supabase';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrors 
} from '../utils/response';

// 创建路由实例
const users = new Hono<{ Bindings: Env; Variables: Variables }>();

// 输入验证 schema
const updateProfileSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
  language_preference: z.enum(['zh', 'en']).optional(),
  subscription_status: z.enum(['free', 'premium', 'vip']).optional()
});

const registerUserSchema = z.object({
  clerk_user_id: z.string().min(1),
  email: z.string().email(),
  name: z.string().min(1).max(100).optional(),
  language_preference: z.enum(['zh', 'en']).default('zh'),
  credits: z.number().int().min(0).default(1)
});

/**
 * POST /v1/users/register - 用户注册
 */
users.post('/register', async (c) => {
  const context = c.get('context');

  try {
    // 验证输入
    const body = await c.req.json();
    const validation = registerUserSchema.safeParse(body);

    if (!validation.success) {
      const apiError = ApiErrors.badRequest(
        'Invalid input data',
        validation.error.errors
      );
      return createErrorResponse(apiError, context.requestId);
    }

    const userData = validation.data;

    const supabaseService = new SupabaseService(c.env);

    // 检查用户是否已存在
    const existingUser = await supabaseService.getUser(userData.clerk_user_id);
    if (existingUser) {
      const apiError = ApiErrors.badRequest('User already exists');
      return createErrorResponse(apiError, context.requestId);
    }

    // 创建用户
    const newUser = await supabaseService.upsertUser({
      clerk_user_id: userData.clerk_user_id,
      email: userData.email,
      name: userData.name || '',
      credits: userData.credits,
      language_preference: userData.language_preference,
      subscription_status: 'free',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    if (!newUser) {
      const apiError = ApiErrors.internalError('Failed to create user');
      return createErrorResponse(apiError, context.requestId);
    }

    // 记录积分交易（注册奖励）
    if (userData.credits > 0) {
      await supabaseService.addCreditTransaction(
        newUser.id,
        userData.credits,
        'bonus',
        'Registration welcome bonus'
      );
    }

    return createSuccessResponse({
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        credits: newUser.credits,
        language_preference: newUser.language_preference,
        subscription_status: newUser.subscription_status,
        created_at: newUser.created_at
      }
    }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to register user:`, error);

    const apiError = ApiErrors.internalError('Failed to register user');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/users/profile - 获取用户信息
 */
users.get('/profile', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    const supabaseService = new SupabaseService(c.env);
    const user = await supabaseService.getUser(userId);
    
    if (!user) {
      const apiError = ApiErrors.notFound('User not found');
      return createErrorResponse(apiError, context.requestId);
    }

    // 获取用户积分
    const credits = await supabaseService.getUserCredits(userId);
    
    // 获取用户的解读历史数量
    const readings = await supabaseService.getUserReadings(userId, 1);
    const totalReadings = readings.length; // 这里应该是总数，但为了简化先用这个

    const profile = {
      ...user,
      credits,
      totalReadings,
      // 移除敏感信息
      id: user.id,
      email: user.email,
      subscription_status: user.subscription_status,
      created_at: user.created_at,
      updated_at: user.updated_at
    };

    return createSuccessResponse(profile, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get user profile:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve user profile');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * PUT /v1/users/profile - 更新用户信息
 */
users.put('/profile', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    // 验证输入
    const body = await c.req.json();
    const validation = updateProfileSchema.safeParse(body);
    
    if (!validation.success) {
      const apiError = ApiErrors.badRequest(
        'Invalid input data',
        validation.error.errors
      );
      return createErrorResponse(apiError, context.requestId);
    }

    const updateData = validation.data;

    const supabaseService = new SupabaseService(c.env);
    
    // 更新用户信息
    const updatedUser = await supabaseService.upsertUser({
      id: userId,
      ...updateData,
      updated_at: new Date().toISOString()
    });

    if (!updatedUser) {
      const apiError = ApiErrors.internalError('Failed to update user profile');
      return createErrorResponse(apiError, context.requestId);
    }

    return createSuccessResponse(updatedUser, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to update user profile:`, error);
    
    const apiError = ApiErrors.internalError('Failed to update user profile');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * POST /v1/users/verify-auth - 验证用户认证状态
 */
users.post('/verify-auth', async (c) => {
  const context = c.get('context');
  
  try {
    const body = await c.req.json();
    const token = body.token;
    
    if (!token) {
      const apiError = ApiErrors.badRequest('Token is required');
      return createErrorResponse(apiError, context.requestId);
    }

    const supabaseService = new SupabaseService(c.env);
    const user = await supabaseService.verifyUserAuth(token);
    
    if (!user) {
      const apiError = ApiErrors.unauthorized('Invalid or expired token');
      return createErrorResponse(apiError, context.requestId);
    }

    // 获取用户积分
    const credits = await supabaseService.getUserCredits(user.id);

    const authResult = {
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        subscription_status: user.subscription_status,
        credits
      }
    };

    return createSuccessResponse(authResult, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to verify auth:`, error);
    
    const apiError = ApiErrors.internalError('Failed to verify authentication');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/users/stats - 获取用户统计信息
 */
users.get('/stats', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    const supabaseService = new SupabaseService(c.env);
    
    // 获取用户基本信息
    const user = await supabaseService.getUser(userId);
    if (!user) {
      const apiError = ApiErrors.notFound('User not found');
      return createErrorResponse(apiError, context.requestId);
    }

    // 获取积分信息
    const credits = await supabaseService.getUserCredits(userId);
    
    // 获取解读历史
    const readings = await supabaseService.getUserReadings(userId, 100); // 获取更多记录来统计
    
    // 获取积分交易历史
    const transactions = await supabaseService.getCreditTransactions(userId, 100);
    
    // 计算统计信息
    const totalReadings = readings.length;
    const totalCreditsSpent = transactions
      .filter(t => t.type === 'consume')
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);
    
    const totalCreditsPurchased = transactions
      .filter(t => t.type === 'purchase')
      .reduce((sum, t) => sum + t.amount, 0);

    // 计算注册天数
    const registrationDate = new Date(user.created_at);
    const daysSinceRegistration = Math.floor(
      (Date.now() - registrationDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    const stats = {
      user: {
        id: user.id,
        email: user.email,
        subscription_status: user.subscription_status,
        memberSince: user.created_at,
        daysSinceRegistration
      },
      credits: {
        current: credits,
        totalPurchased: totalCreditsPurchased,
        totalSpent: totalCreditsSpent
      },
      readings: {
        total: totalReadings,
        thisMonth: readings.filter(r => {
          const readingDate = new Date(r.created_at);
          const now = new Date();
          return readingDate.getMonth() === now.getMonth() && 
                 readingDate.getFullYear() === now.getFullYear();
        }).length
      },
      activity: {
        lastReadingDate: readings[0]?.created_at || null,
        lastTransactionDate: transactions[0]?.created_at || null
      }
    };

    return createSuccessResponse(stats, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get user stats:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve user statistics');
    return createErrorResponse(apiError, context.requestId);
  }
});

export default users;
