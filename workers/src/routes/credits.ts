import { Hono } from 'hono';
import { z } from 'zod';
import type { Env, Variables } from '../types';
import { SupabaseService } from '../services/supabase';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrors 
} from '../utils/response';

// 创建路由实例
const credits = new Hono<{ Bindings: Env; Variables: Variables }>();

// 输入验证 schema
const consumeCreditsSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().min(1, 'Description is required')
});

const addCreditsSchema = z.object({
  amount: z.number().int().positive(),
  type: z.enum(['purchase', 'bonus', 'refund', 'admin_adjustment']),
  description: z.string().min(1).max(255),
  reference_id: z.string().optional()
});

/**
 * GET /v1/credits - 获取用户积分余额
 */
credits.get('/', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    const supabaseService = new SupabaseService(c.env);
    const balance = await supabaseService.getUserCredits(userId);

    return createSuccessResponse({ balance }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get credits:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve credits');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * POST /v1/credits/add - 添加积分
 */
credits.post('/add', async (c) => {
  const context = c.get('context');

  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    // 验证输入
    const body = await c.req.json();
    const validation = addCreditsSchema.safeParse(body);

    if (!validation.success) {
      const apiError = ApiErrors.badRequest(
        'Invalid input data',
        validation.error.errors
      );
      return createErrorResponse(apiError, context.requestId);
    }

    const { amount, type, description, reference_id } = validation.data;

    const supabaseService = new SupabaseService(c.env);

    // 获取当前用户信息
    const user = await supabaseService.getUser(userId);
    if (!user) {
      const apiError = ApiErrors.notFound('User not found');
      return createErrorResponse(apiError, context.requestId);
    }

    // 添加积分
    const success = await supabaseService.addCredits(
      user.id,
      amount,
      type,
      description,
      reference_id
    );

    if (!success) {
      const apiError = ApiErrors.internalError('Failed to add credits');
      return createErrorResponse(apiError, context.requestId);
    }

    // 获取更新后的积分余额
    const newBalance = await supabaseService.getUserCredits(userId);

    return createSuccessResponse({
      success: true,
      added: amount,
      newBalance,
      transaction: {
        type,
        description,
        reference_id
      }
    }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to add credits:`, error);

    const apiError = ApiErrors.internalError('Failed to add credits');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * POST /v1/credits/consume - 消费积分
 */
credits.post('/consume', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    // 验证输入
    const body = await c.req.json();
    const validation = consumeCreditsSchema.safeParse(body);
    
    if (!validation.success) {
      const apiError = ApiErrors.badRequest(
        'Invalid input data',
        validation.error.errors
      );
      return createErrorResponse(apiError, context.requestId);
    }

    const { amount, description } = validation.data;

    const supabaseService = new SupabaseService(c.env);
    
    // 检查当前余额
    const currentBalance = await supabaseService.getUserCredits(userId);
    if (currentBalance < amount) {
      const apiError = ApiErrors.badRequest(
        `Insufficient credits. Required: ${amount}, Available: ${currentBalance}`
      );
      return createErrorResponse(apiError, context.requestId);
    }

    // 消费积分
    const success = await supabaseService.consumeCredits(userId, amount, description);
    
    if (!success) {
      const apiError = ApiErrors.internalError('Failed to consume credits');
      return createErrorResponse(apiError, context.requestId);
    }

    // 获取更新后的余额
    const newBalance = await supabaseService.getUserCredits(userId);

    const result = {
      consumed: amount,
      description,
      previousBalance: currentBalance,
      newBalance,
      timestamp: new Date().toISOString()
    };

    return createSuccessResponse(result, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to consume credits:`, error);
    
    const apiError = ApiErrors.internalError('Failed to consume credits');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/credits/transactions - 获取积分交易历史
 */
credits.get('/transactions', async (c) => {
  const context = c.get('context');
  
  try {
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    const supabaseService = new SupabaseService(c.env);
    const limit = parseInt(c.req.query('limit') || '50');
    
    const transactions = await supabaseService.getCreditTransactions(userId, limit);

    return createSuccessResponse({ transactions }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get credit transactions:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve transactions');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/credits/packages - 获取积分套餐信息
 */
credits.get('/packages', async (c) => {
  const context = c.get('context');
  
  try {
    const supabaseService = new SupabaseService(c.env);

    // 从数据库获取积分套餐信息
    const packages = await supabaseService.getCreditPackages();

    return createSuccessResponse({ packages }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get credit packages:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve packages');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/credits/config - 获取积分相关配置
 */
credits.get('/config', async (c) => {
  const context = c.get('context');
  
  try {
    const supabaseService = new SupabaseService(c.env);
    
    const [readingCost, premiumCost] = await Promise.all([
      supabaseService.getAppConfig('READING_COST_CREDITS'),
      supabaseService.getAppConfig('PREMIUM_CONTENT_COST_CREDITS')
    ]);

    const config = {
      readingCost: parseInt(readingCost || '10'),
      premiumContentCost: parseInt(premiumCost || '5'),
      currency: 'credits'
    };

    return createSuccessResponse(config, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get credit config:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve configuration');
    return createErrorResponse(apiError, context.requestId);
  }
});

export default credits;
