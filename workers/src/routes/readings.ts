import { Hono } from 'hono';
import { z } from 'zod';
import type { Env, Variables, BirthInfo } from '../types';
import { SupabaseService } from '../services/supabase';
import { DeepSeekService } from '../services/deepseek';
import { PromptService } from '../services/prompt';
import { BaZiCalculator } from '../services/bazi';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrors,
  generateRequestId 
} from '../utils/response';

// 创建路由实例
const readings = new Hono<{ Bindings: Env; Variables: Variables }>();

// 输入验证 schema
const createReadingSchema = z.object({
  birthInfo: z.object({
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    time: z.string().regex(/^\d{2}:\d{2}$/, 'Time must be in HH:MM format'),
    location: z.string().min(1, 'Location is required'),
    timezone: z.string().optional(),
    gender: z.enum(['male', 'female'])
  })
});

/**
 * POST /v1/readings - 创建八字分析
 */
readings.post('/', async (c) => {
  const context = c.get('context');
  
  try {
    // 验证输入
    const body = await c.req.json();
    const validation = createReadingSchema.safeParse(body);
    
    if (!validation.success) {
      const apiError = ApiErrors.badRequest(
        'Invalid input data',
        validation.error.errors
      );
      return createErrorResponse(apiError, context.requestId);
    }

    const { birthInfo } = validation.data;

    // 初始化服务
    const supabaseService = new SupabaseService(c.env);
    const promptService = new PromptService(supabaseService.supabase);
    const deepseekService = new DeepSeekService(c.env, promptService);
    const baziCalculator = new BaZiCalculator();

    // 获取用户ID（从认证中间件设置）
    const userId = context.userId;
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    // 检查用户积分
    const userCredits = await supabaseService.getUserCredits(userId);
    const readingCost = parseInt(await supabaseService.getAppConfig('READING_COST_CREDITS') || '10');
    
    if (userCredits < readingCost) {
      const apiError = ApiErrors.badRequest(
        `Insufficient credits. Required: ${readingCost}, Available: ${userCredits}`
      );
      return createErrorResponse(apiError, context.requestId);
    }

    // 步骤1：计算八字
    console.log(`[${context.requestId}] Calculating BaZi chart...`);
    const baziChart = baziCalculator.calculateBaZi(birthInfo);
    
    // 步骤2：计算五行分布
    const elementProfile = baziCalculator.calculateElementProfile(baziChart);
    
    // 步骤3：使用 DeepSeek AI 进行命理分析
    console.log(`[${context.requestId}] Analyzing destiny with AI...`);
    const destinyAnalysis = await deepseekService.analyzeDestiny(baziChart, elementProfile);
    
    // 步骤4：生成详细解读
    console.log(`[${context.requestId}] Generating detailed readings...`);
    const readingContent = await deepseekService.generateReadings(destinyAnalysis);
    
    // 步骤5：生成水晶推荐
    console.log(`[${context.requestId}] Generating crystal recommendations...`);
    const crystalRecommendations = await deepseekService.generateCrystalRecommendations(elementProfile);

    // 步骤6：消费积分
    console.log(`[${context.requestId}] Consuming credits...`);
    const creditConsumed = await supabaseService.consumeCredits(
      userId,
      readingCost,
      'BaZi reading analysis'
    );

    if (!creditConsumed) {
      const apiError = ApiErrors.internalError('Failed to consume credits');
      return createErrorResponse(apiError, context.requestId);
    }

    // 步骤7：保存分析结果
    console.log(`[${context.requestId}] Saving reading results...`);

    // 获取用户的实际 UUID
    const user = await supabaseService.getUser(userId);
    if (!user) {
      const apiError = ApiErrors.internalError('User not found');
      return createErrorResponse(apiError, context.requestId);
    }

    const readingId = await supabaseService.saveReading({
      user_id: user.id, // 使用数据库中的 UUID
      birth_date: birthInfo.date,
      birth_time: birthInfo.time,
      birth_location: { location: birthInfo.location },
      timezone: birthInfo.timezone || 'Asia/Shanghai',
      gender: birthInfo.gender,
      result: {
        bazi_chart: baziChart,
        destiny_analysis: destinyAnalysis,
        reading_content: readingContent,
        element_profile: elementProfile,
        crystal_recommendations: crystalRecommendations
      },
      summary: destinyAnalysis?.summary || 'BaZi analysis completed'
    });

    if (!readingId) {
      const apiError = ApiErrors.internalError('Failed to save reading');
      return createErrorResponse(apiError, context.requestId);
    }

    // 返回完整结果
    const result = {
      id: readingId,
      baziChart,
      elementProfile,
      destinyAnalysis,
      readingContent,
      crystalRecommendations,
      creditsConsumed: readingCost,
      remainingCredits: userCredits - readingCost
    };

    console.log(`[${context.requestId}] Reading analysis completed successfully`);
    return createSuccessResponse(result, context.requestId, 201);

  } catch (error) {
    console.error(`[${context.requestId}] Reading analysis failed:`, error);
    
    const apiError = error instanceof Error 
      ? ApiErrors.internalError(error.message)
      : ApiErrors.internalError('An unexpected error occurred during analysis');
    
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/readings/:id - 获取分析结果
 */
readings.get('/:id', async (c) => {
  const context = c.get('context');
  const readingId = c.req.param('id');
  
  try {
    const supabaseService = new SupabaseService(c.env);
    const userId = context.userId;

    // 获取分析结果
    const reading = await supabaseService.getReading(readingId, userId);
    
    if (!reading) {
      const apiError = ApiErrors.notFound('Reading not found');
      return createErrorResponse(apiError, context.requestId);
    }

    return createSuccessResponse(reading, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get reading:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve reading');
    return createErrorResponse(apiError, context.requestId);
  }
});

/**
 * GET /v1/readings - 获取用户的分析历史
 */
readings.get('/', async (c) => {
  const context = c.get('context');
  
  try {
    const supabaseService = new SupabaseService(c.env);
    const userId = context.userId;
    
    if (!userId) {
      const apiError = ApiErrors.unauthorized('Authentication required');
      return createErrorResponse(apiError, context.requestId);
    }

    const limit = parseInt(c.req.query('limit') || '20');
    const readings = await supabaseService.getUserReadings(userId, limit);

    return createSuccessResponse({ readings }, context.requestId);

  } catch (error) {
    console.error(`[${context.requestId}] Failed to get user readings:`, error);
    
    const apiError = ApiErrors.internalError('Failed to retrieve readings');
    return createErrorResponse(apiError, context.requestId);
  }
});

export default readings;
