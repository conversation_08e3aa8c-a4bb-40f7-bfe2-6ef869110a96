// Cloudflare Workers 环境类型
export interface Env {
  // KV 命名空间
  CACHE: KVNamespace;

  // 环境变量（从数据库读取）
  ENVIRONMENT: string;
  API_VERSION: string;

  // 数据库配置（从 Supabase 配置表读取）
  SUPABASE_URL?: string;
  SUPABASE_SERVICE_KEY?: string;

  // AI 服务配置（从数据库读取）
  DEEPSEEK_API_KEY?: string;
  DEEPSEEK_BASE_URL?: string;
}

// Hono 上下文变量类型
export interface Variables {
  context: RequestContext;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

// 八字相关类型（从前端复制）
export interface BirthInfo {
  date: string;
  time: string;
  location: string;
  timezone: string;
  gender: 'male' | 'female';
}

export interface BaZiChart {
  year: { heavenly: string; earthly: string; element: string };
  month: { heavenly: string; earthly: string; element: string };
  day: { heavenly: string; earthly: string; element: string };
  hour: { heavenly: string; earthly: string; element: string };
}

export interface ElementProfile {
  wood: number;
  fire: number;
  earth: number;
  metal: number;
  water: number;
}

export interface DestinyAnalysis {
  overall: string;
  elements: string;
  personality: string;
  strengths: string;
  challenges: string;
  advice: string;
}

export interface ReadingContent {
  career: string;
  relationships: string;
  health: string;
  wealth: string;
  development: string;
}

export interface CrystalRecommendation {
  name: string;
  nameZh: string;
  element: string;
  benefit: string;
  benefitZh: string;
  reason: string;
}

// 用户相关类型
export interface User {
  id: string;
  email: string;
  credits: number;
  subscription_status: string;
  created_at: string;
  updated_at: string;
}

// 积分交易类型
export interface CreditTransaction {
  id: string;
  user_id: string;
  type: 'purchase' | 'consume' | 'refund' | 'bonus' | 'admin_adjust' | 'expire';
  amount: number;
  description: string;
  created_at: string;
}

// 配置类型（从数据库读取）
export interface AppConfig {
  key: string;
  value: string;
  description?: string;
  is_secret: boolean;
  created_at: string;
  updated_at: string;
}

// 请求上下文类型
export interface RequestContext {
  requestId: string;
  userId?: string;
  userAgent?: string;
  ip?: string;
  timestamp: string;
}

// 完整分析结果类型
export interface CompleteAnalysis {
  baziChart: BaZiChart;
  elementProfile: ElementProfile;
  destinyAnalysis: DestinyAnalysis;
  readingContent: ReadingContent;
  crystalRecommendations: CrystalRecommendation[];
}
