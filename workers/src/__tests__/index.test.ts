import { describe, it, expect, beforeEach } from 'vitest';
import worker from '../index';
import type { Env } from '../types';

// 模拟环境变量
const mockEnv: Env = {
  CACHE: {} as KVNamespace,
  ENVIRONMENT: 'test',
  API_VERSION: 'v1',
  SUPABASE_URL: 'https://test.supabase.co',
  SUPABASE_SERVICE_KEY: 'test-key',
  DEEPSEEK_API_KEY: 'test-deepseek-key',
  DEEPSEEK_BASE_URL: 'https://api.deepseek.com'
};

describe('Workers API', () => {
  let ctx: ExecutionContext;

  beforeEach(() => {
    ctx = {
      waitUntil: () => {},
      passThroughOnException: () => {},
      props: {}
    } as ExecutionContext;
  });

  it('should respond to health check', async () => {
    const request = new Request('http://localhost/health');
    const response = await worker.fetch(request, mockEnv, ctx);
    
    expect(response.status).toBe(200);
    
    const data = await response.json() as any;
    expect(data.success).toBe(true);
    expect(data.data.status).toBe('healthy');
  });

  it('should respond to test endpoint', async () => {
    const request = new Request('http://localhost/v1/test');
    const response = await worker.fetch(request, mockEnv, ctx);
    
    expect(response.status).toBe(200);
    
    const data = await response.json() as any;
    expect(data.success).toBe(true);
    expect(data.data.message).toContain('Eastern Fate Master API');
  });

  it('should return 404 for unknown endpoints', async () => {
    const request = new Request('http://localhost/unknown');
    const response = await worker.fetch(request, mockEnv, ctx);
    
    expect(response.status).toBe(404);
    
    const data = await response.json() as any;
    expect(data.success).toBe(false);
    expect(data.error.code).toBe('NOT_FOUND');
  });

  it('should handle CORS preflight requests', async () => {
    const request = new Request('http://localhost/v1/test', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:5173',
        'Access-Control-Request-Method': 'POST'
      }
    });
    
    const response = await worker.fetch(request, mockEnv, ctx);
    
    expect(response.status).toBe(200);
    expect(response.headers.get('Access-Control-Allow-Origin')).toBe('http://localhost:5173');
  });
});
