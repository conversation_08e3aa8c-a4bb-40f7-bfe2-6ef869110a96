# Cloudflare Workers 本地开发环境变量示例
# 注意：生产环境的所有配置都从数据库读取

# 环境标识
ENVIRONMENT=development
API_VERSION=v1

# Supabase 配置（本地开发用，生产环境从数据库读取）
SUPABASE_URL=https://tagcspajmlnnnwdrwtvf.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# DeepSeek API 配置（本地开发用，生产环境从数据库读取）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# KV 命名空间 ID（需要在 Cloudflare 控制台创建）
KV_NAMESPACE_ID=your_kv_namespace_id_here
KV_PREVIEW_ID=your_preview_kv_namespace_id_here

# 开发服务器配置
DEV_PORT=8787
DEV_HOST=localhost
