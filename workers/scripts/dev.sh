#!/bin/bash

# Eastern Fate Master Workers 开发脚本
# 用于启动本地开发环境

echo "🚀 启动 Eastern Fate Master Workers 开发环境..."

# 检查是否安装了依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，从 .env.example 复制..."
    cp .env.example .env
    echo "请编辑 .env 文件并设置正确的配置值"
    exit 1
fi

# 运行类型检查
echo "🔍 运行 TypeScript 类型检查..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "❌ TypeScript 类型检查失败"
    exit 1
fi

# 运行 ESLint
echo "🔍 运行代码质量检查..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  代码质量检查发现问题，但继续启动开发服务器..."
fi

# 启动开发服务器
echo "🌟 启动 Wrangler 开发服务器..."
echo "📍 API 将在 http://localhost:8787 上运行"
echo "🔗 健康检查: http://localhost:8787/health"
echo "🔗 测试端点: http://localhost:8787/v1/test"
echo ""
echo "按 Ctrl+C 停止服务器"

npm run dev
