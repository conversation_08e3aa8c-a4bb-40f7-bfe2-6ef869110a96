name = "eastern-fate-master-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境配置
[env.development]
name = "eastern-fate-master-api-dev"

[env.staging]
name = "eastern-fate-master-api-staging"

[env.production]
name = "eastern-fate-master-api"

# KV 命名空间配置（用于缓存）
[[kv_namespaces]]
binding = "CACHE"
id = "your_kv_namespace_id_here"
preview_id = "your_preview_kv_namespace_id_here"

# 环境变量（从数据库读取，这里只是占位符）
[vars]
ENVIRONMENT = "development"
API_VERSION = "v1"
SUPABASE_URL = "https://tagcspajmlnnnwdrwtvf.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRhZ2NzcGFqbWxubm53ZHJ3dHZmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTkxODUzNywiZXhwIjoyMDY1NDk0NTM3fQ.aLDPpRuWX2vwR9YShIf4BZPQG8J7W9fmOiyOPyWF1nY"
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# Durable Objects（如果需要）
# [[durable_objects.bindings]]
# name = "RATE_LIMITER"
# class_name = "RateLimiter"

# 路由配置
[triggers]
crons = []

# 构建配置
[build]
command = "npm run build"

# 开发配置
[dev]
ip = "localhost"
port = 8787
local_protocol = "http"
