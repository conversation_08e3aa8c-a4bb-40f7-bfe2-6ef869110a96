# Eastern Fate Master - Cloudflare Workers API

这是 Eastern Fate Master 项目的 Cloudflare Workers 后端 API 服务。

## 🏗️ 项目结构

```
workers/
├── src/
│   ├── index.ts              # 主入口文件
│   ├── types/                # TypeScript 类型定义
│   ├── services/             # 业务服务
│   │   └── config.ts         # 配置服务（从数据库读取）
│   ├── middleware/           # 中间件
│   ├── routes/               # API 路由
│   ├── utils/                # 工具函数
│   │   └── response.ts       # 响应处理工具
│   └── __tests__/            # 测试文件
├── scripts/
│   └── dev.sh               # 开发启动脚本
├── wrangler.toml            # Cloudflare Workers 配置
├── package.json             # 项目依赖
├── tsconfig.json            # TypeScript 配置
└── vitest.config.ts         # 测试配置
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，设置正确的配置值
```

### 3. 启动开发服务器

```bash
# 使用开发脚本（推荐）
./scripts/dev.sh

# 或直接使用 npm
npm run dev
```

### 4. 测试 API

访问以下端点测试 API：

- 健康检查: http://localhost:8787/health
- 测试端点: http://localhost:8787/v1/test
- 配置信息: http://localhost:8787/v1/config (仅开发环境)

## 🔧 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 运行测试
npm run test

# 监听模式运行测试
npm run test:watch

# 代码质量检查
npm run lint

# TypeScript 类型检查
npm run type-check
```

## 📊 API 端点

### 基础端点

- `GET /health` - 健康检查
- `GET /v1/test` - 测试端点
- `GET /v1/config` - 配置信息（仅开发环境）

### 即将实现的端点

- `POST /v1/readings` - 创建八字分析
- `GET /v1/readings/:id` - 获取分析结果
- `POST /v1/credits/consume` - 消费积分
- `GET /v1/users/profile` - 获取用户信息

## 🔒 配置管理

本项目按照要求，所有环境变量都从数据库读取，不做文件硬编码：

- 生产环境：所有配置从 `app_configs` 表读取
- 开发环境：优先从数据库读取，fallback 到环境变量

### 配置表结构

```sql
CREATE TABLE app_configs (
  id UUID PRIMARY KEY,
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN DEFAULT false,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

## 🧪 测试

```bash
# 运行所有测试
npm run test

# 监听模式
npm run test:watch
```

## 🚀 部署

### 开发环境

```bash
npm run deploy
```

### 生产环境

```bash
npm run deploy:production
```

## 📝 开发注意事项

1. **配置管理**: 所有敏感配置都存储在数据库中
2. **错误处理**: 使用统一的错误响应格式
3. **日志记录**: 每个请求都有唯一的 requestId
4. **类型安全**: 严格的 TypeScript 类型检查
5. **测试覆盖**: 为所有核心功能编写测试

## 🔗 相关链接

- [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- [Hono 框架文档](https://hono.dev/)
- [Supabase 文档](https://supabase.com/docs)
