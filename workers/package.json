{"name": "eastern-fate-master-workers", "version": "1.0.0", "description": "Cloudflare Workers API for Eastern Fate Master", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "build": "esbuild src/index.ts --bundle --outfile=dist/index.js --format=esm --target=es2022", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "hono": "^4.0.0", "zod": "^3.22.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240117.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.18.0", "@typescript-eslint/parser": "^6.18.0", "esbuild": "^0.19.0", "eslint": "^8.56.0", "miniflare": "^4.20250705.0", "typescript": "^5.3.0", "vitest": "^1.1.0", "vitest-environment-miniflare": "^2.14.4", "wrangler": "^3.24.0"}, "keywords": ["cloudflare-workers", "api", "bazi", "eastern-fate-master"], "author": "Eastern Fate Master Team", "license": "MIT"}