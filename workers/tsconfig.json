{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}