import { useState, useEffect, useCallback } from 'react';
import { User } from '@supabase/supabase-js';
import { authService, AuthUser, AuthState, AuthProvider } from '../lib/auth';

export const useAuth = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const setUser = (user: AuthUser | null) => {
    setState(prev => ({ ...prev, user, loading: false }));
  };

  const signUp = useCallback(async (email: string, password: string, name?: string) => {
    setLoading(true);
    setError(null);

    const { data, error } = await authService.signUp(email, password, name);

    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }

    // 注册成功后获取用户信息
    if (data?.user) {
      try {
        const userInfo = await authService.getCurrentUser();
        if (userInfo.data) {
          setUser(userInfo.data);
        }
      } catch (err) {
        console.warn('Failed to get user info after signup:', err);
      }
    }

    setLoading(false);
    return { success: true, data };
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    const { data, error } = await authService.signIn(email, password);

    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }

    // 登录成功后获取用户信息
    if (data?.user) {
      try {
        const userInfo = await authService.getCurrentUser();
        if (userInfo.data) {
          setUser(userInfo.data);
        }
      } catch (err) {
        console.warn('Failed to get user info after login:', err);
      }
    }

    setLoading(false);
    return { success: true, data };
  }, []);

  const signOut = useCallback(async () => {
    setLoading(true);
    const { error } = await authService.signOut();
    
    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }
    
    setUser(null);
    return { success: true };
  }, []);

  const signInWithProvider = useCallback(async (provider: AuthProvider) => {
    setLoading(true);
    setError(null);
    
    const { data, error } = await authService.signInWithProvider(provider);
    
    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }
    
    // OAuth redirect will handle the rest
    return { success: true, data };
  }, []);

  const updateProfile = useCallback(async (updates: Partial<AuthUser>) => {
    if (!state.user) return { success: false, error: 'No user logged in' };
    
    const { data, error } = await authService.updateProfile(updates);
    
    if (error) {
      setError(error);
      return { success: false, error };
    }
    
    if (data) {
      setUser({ ...state.user, ...updates });
    }
    
    return { success: true, data };
  }, [state.user]);

  const refreshUser = useCallback(async () => {
    const user = await authService.getCurrentUser();
    setUser(user);
  }, []);

  useEffect(() => {
    // Initialize auth state
    const initAuth = async () => {
      const user = await authService.getCurrentUser();
      setUser(user);
    };

    initAuth();

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(async (user: User | null) => {
      if (user) {
        const authUser = await authService.getCurrentUser();
        setUser(authUser);
      } else {
        setUser(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    user: state.user,
    loading: state.loading,
    error: state.error,
    signUp,
    signIn,
    signOut,
    signInWithProvider,
    updateProfile,
    refreshUser,
    isAuthenticated: !!state.user
  };
};