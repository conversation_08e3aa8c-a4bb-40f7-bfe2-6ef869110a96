import { useState, useEffect, useCallback } from 'react';
import { creditService, CreditTransaction, CreditPackage } from '../lib/credits';
import { useAuth } from './useAuth';

export const useCredits = () => {
  const { user } = useAuth();
  const [credits, setCredits] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshCredits = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    const { credits: userCredits, error } = await creditService.getUserCredits(user.id);
    
    if (error) {
      setError(error);
    } else {
      setCredits(userCredits);
      setError(null);
    }
    
    setLoading(false);
  }, [user]);

  const consumeCredits = useCallback(async (
    amount: number,
    type: string = 'reading',
    referenceId?: string,
    description?: string
  ) => {
    if (!user) return { success: false, error: 'User not authenticated' };
    
    setLoading(true);
    const result = await creditService.consumeCredits(user.id, amount, type, referenceId, description);
    
    if (result.success) {
      setCredits(prev => prev - amount);
      setError(null);
    } else {
      setError(result.error || 'Failed to consume credits');
    }
    
    setLoading(false);
    return result;
  }, [user]);

  const addCredits = useCallback(async (
    amount: number,
    type: 'purchase' | 'bonus' | 'admin_adjust' = 'purchase',
    referenceId?: string,
    description?: string
  ) => {
    if (!user) return { success: false, error: 'User not authenticated' };
    
    setLoading(true);
    const result = await creditService.addCredits(user.id, amount, type, referenceId, description);
    
    if (result.success) {
      setCredits(prev => prev + amount);
      setError(null);
    } else {
      setError(result.error || 'Failed to add credits');
    }
    
    setLoading(false);
    return result;
  }, [user]);

  useEffect(() => {
    if (user) {
      refreshCredits();
    } else {
      setCredits(0);
    }
  }, [user, refreshCredits]);

  return {
    credits,
    loading,
    error,
    refreshCredits,
    consumeCredits,
    addCredits,
    hasEnoughCredits: (amount: number) => credits >= amount
  };
};

export const useCreditTransactions = (page: number = 1, limit: number = 20, type?: string) => {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    const { transactions, total, error } = await creditService.getCreditTransactions(user.id, page, limit, type);
    
    if (error) {
      setError(error);
    } else {
      setTransactions(transactions);
      setTotal(total);
      setError(null);
    }
    
    setLoading(false);
  }, [user, page, limit, type]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  return {
    transactions,
    total,
    loading,
    error,
    refetch: fetchTransactions
  };
};

export const useCreditPackages = () => {
  const [packages, setPackages] = useState<CreditPackage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPackages = useCallback(async () => {
    setLoading(true);
    const { packages, error } = await creditService.getCreditPackages();
    
    if (error) {
      setError(error);
    } else {
      setPackages(packages);
      setError(null);
    }
    
    setLoading(false);
  }, []);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  return {
    packages,
    loading,
    error,
    refetch: fetchPackages
  };
};