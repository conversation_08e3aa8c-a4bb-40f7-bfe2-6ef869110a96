import { baziCalculator } from './bazi';
import { deepseekService } from './deepseek';
import { BirthInfo, BaZi<PERSON>hart, ElementProfile, DestinyAnalysis, ReadingContent, CrystalRecommendation } from '../types/index';

export interface CompleteAnalysis {
  birthInfo: BirthInfo;
  baziChart: BaZi<PERSON>hart;
  elementProfile: ElementProfile;
  destinyAnalysis: DestinyAnalysis;
  readingContent: ReadingContent;
  crystalRecommendations: CrystalRecommendation[];
  balance: {
    strongest: string;
    weakest: string;
    balanced: boolean;
    suggestions: string[];
  };
}

class AnalysisService {
  async performCompleteAnalysis(birthInfo: BirthInfo): Promise<CompleteAnalysis> {
    try {
      // 步骤1：计算八字
      const baziChart = baziCalculator.calculateBaZi(birthInfo);
      
      // 步骤2：计算五行分布
      const elementProfile = baziCalculator.calculateElementProfile(baziChart);
      
      // 步骤3：获取五行平衡建议
      const balance = baziCalculator.getElementBalance(elementProfile);
      
      // 步骤4：使用 DeepSeek AI 进行命理分析
      const destinyAnalysis = await deepseekService.analyzeDestiny(baziChart, elementProfile);
      
      // 步骤5：生成详细解读
      const readingContent = await deepseekService.generateReadings(destinyAnalysis);
      
      // 步骤6：生成水晶推荐
      const crystalRecommendations = await deepseekService.generateCrystalRecommendations(elementProfile);
      
      return {
        birthInfo,
        baziChart,
        elementProfile,
        destinyAnalysis,
        readingContent,
        crystalRecommendations,
        balance
      };
    } catch (error) {
      console.error('Analysis service error:', error);
      throw new Error('分析服务暂时不可用，请稍后重试');
    }
  }

  // 保存分析结果到本地存储
  saveAnalysisToStorage(analysis: CompleteAnalysis) {
    try {
      localStorage.setItem('analysisResult', JSON.stringify(analysis));
    } catch (error) {
      console.error('Failed to save analysis result:', error);
    }
  }

  // 从本地存储获取分析结果
  getAnalysisFromStorage(): CompleteAnalysis | null {
    try {
      const stored = localStorage.getItem('analysisResult');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get analysis result:', error);
      return null;
    }
  }

  // 清理本地存储
  clearStoredAnalysis() {
    try {
      localStorage.removeItem('analysisResult');
      localStorage.removeItem('birthInfo');
    } catch (error) {
      console.error('Failed to clear stored analysis:', error);
    }
  }
}

export const analysisService = new AnalysisService();