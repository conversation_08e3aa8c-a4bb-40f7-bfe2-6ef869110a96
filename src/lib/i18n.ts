import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  en: {
    translation: {
      // Header
      "language": "EN",
      "darkMode": "Dark Mode",
      
      // Home Page
      "title": "Eastern Fate Master",
      "subtitle": "东方玄学大师",
      "description": "Discover your destiny through ancient Chinese metaphysics. Unlock your BaZi chart and receive personalized crystal recommendations.",
      "beginJourney": "Begin Your Journey",
      "enterBirthDate": "Enter your birth date to reveal your Eastern fate",
      
      // Features
      "discoverFeatures": "Discover Our Features",
      "baziAnalysis": "BaZi Analysis",
      "baziDescription": "Detailed reading of your Four Pillars chart to reveal your life path, strengths, and challenges.",
      "fiveElements": "Five Elements Balance",
      "fiveElementsDescription": "Understand your personal elemental makeup and how to optimize balance in your life.",
      "crystalRecommendations": "Crystal Recommendations",
      "crystalDescription": "Receive personalized crystal and stone suggestions based on your elemental needs.",
      
      // Testimonials
      "userTestimonials": "What Our Users Say",
      "sarahTestimonial": "The BaZi reading was incredibly accurate! I now understand myself better and have found crystals that truly resonate with my energy.",
      "liWeiTestimonial": "Eastern Fate Master combines traditional wisdom with modern insights. The crystal recommendations have brought harmony to my daily life.",
      
      // Birth Info Form
      "enterBirthInfo": "Enter your birth information",
      "dateOfBirth": "Date of Birth",
      "birthDateEssential": "Your birthdate is essential for calculating your destiny chart",
      "birthCountry": "Birth Country",
      "birthCity": "Birth City (Optional)",
      "birthCityPlaceholder": "Search for your birth city",
      "birthCityHelp": "Adding your birth city helps with more accurate time zone settings",
      "continue": "Continue",
      "freeReadingsRemaining": "Free readings remaining today:",
      
      // Loading Page
      "consultingStars": "Consulting the Stars...",
      "personalizedReading": "Your personalized destiny reading is being crafted",
      "analyzingPositions": "Analyzing the positions of celestial bodies at the moment of your birth",
      "typicallyTakes": "Typically takes 20-40 seconds",
      "ancientWisdom": "An ancient Chinese proverb says: \"Patience reveals the path to wisdom\"",
      "preparingReading": "Preparing your personalized reading...",
      
      // Results Page
      "fateReading": "Your Fate Reading",
      "baziChart": "Your BaZi Chart",
      "fiveElementsProfile": "Five Elements Profile",
      "crystalJadeRecommendations": "Crystal & Jade Recommendations",
      "overallFate": "Overall Fate",
      "careerWealth": "Career & Wealth",
      "relationships": "Relationships & Romance",
      "healthWellness": "Health & Wellness",
      "unlockPremium": "Unlock Premium Insights",
      "premiumDescription": "Get in-depth analysis of your destiny with advanced insights and personalized guidance.",
      "unlockWithCredits": "Unlock with Credits",
      
      // Footer
      "secure": "Secure",
      "private": "Private",
      "readings": "10K+ Readings",
      "allRightsReserved": "All rights reserved.",
      "discoverDestiny": "Discover your destiny through ancient wisdom",
      
      // Elements
      "wood": "Wood",
      "fire": "Fire", 
      "earth": "Earth",
      "metal": "Metal",
      "water": "Water",
      
      // Auth
      "login": "Login",
      "register": "Register",
      "logout": "Logout",
      "signOut": "Sign Out",
      "email": "Email",
      "password": "Password",
      "confirmPassword": "Confirm Password",
      "name": "Name",
      "enterEmail": "Enter your email",
      "enterPassword": "Enter your password",
      "enterName": "Enter your name",
      "noAccount": "Don't have an account?",
      "haveAccount": "Already have an account?",
      "passwordsDoNotMatch": "Passwords do not match",
      "loading": "Loading...",
      "continueWithGoogle": "Continue with Google",
      "continueWithGitHub": "Continue with GitHub", 
      "continueWithDiscord": "Continue with Discord",
      "orContinueWith": "Or continue with email",
      "completingSignIn": "Completing sign in",
      "pleaseWait": "Please wait while we complete your authentication...",
      
      // Credits
      "credits": "Credits",
      "availableCredits": "Available Credits",
      "purchaseCredits": "Purchase Credits",
      "creditHistory": "Credit History",
      "insufficientCredits": "Insufficient credits",
      "basicReading": "Basic Reading",
      "basicReadingCost": "Basic reading cost",
      "premiumReadingUnlock": "Premium reading unlock",
      "creditConsumptionFailed": "Credit consumption failed",
      "unlockFailed": "Unlock failed",
      "premiumContentLocked": "Premium Content Locked",
      "unlockPremiumDescription": "Unlock detailed insights for all life aspects",
      "mostPopular": "Most Popular",
      "featured": "Featured",
      "validFor": "Valid for",
      "days": "days",
      "purchase": "Purchase",
      "processing": "Processing...",
      "paymentMethods": "Payment Methods",
      "alipay": "Alipay",
      "wechatPay": "WeChat Pay",
      "allTransactions": "All Transactions",
      "purchases": "Purchases",
      "consumption": "Consumption",
      "bonuses": "Bonuses",
      "noTransactions": "No transactions found",
      "balance": "Balance",
      "showing": "Showing",
      "of": "of",
      "previous": "Previous",
      "next": "Next",
      
      // Reading Generation
      "generating": "Generating...",
      "readingGenerationFailed": "Reading generation failed",
      "creditsConsumed": "Credits consumed",
      "remainingCredits": "Remaining credits",
      
      // Countries
      "china": "China",
      "usa": "United States",
      "uk": "United Kingdom",
      "canada": "Canada",
      "australia": "Australia"
    }
  },
  zh: {
    translation: {
      // Header
      "language": "中文",
      "darkMode": "深色模式",
      
      // Home Page
      "title": "Eastern Fate Master",
      "subtitle": "东方玄学大师",
      "description": "通过古老的中国玄学探索您的命运。解锁您的八字命盘，获得个性化的水晶建议。",
      "beginJourney": "开始您的旅程",
      "enterBirthDate": "输入您的出生日期以揭示您的东方命运",
      
      // Features
      "discoverFeatures": "探索我们的功能",
      "baziAnalysis": "八字分析",
      "baziDescription": "详细解读您的四柱命盘，揭示您的人生道路、优势和挑战。",
      "fiveElements": "五行平衡",
      "fiveElementsDescription": "了解您的个人五行构成，以及如何优化生活中的平衡。",
      "crystalRecommendations": "水晶推荐",
      "crystalDescription": "根据您的五行需求，获得个性化的水晶和宝石建议。",
      
      // Testimonials
      "userTestimonials": "用户评价",
      "sarahTestimonial": "八字解读非常准确！我现在更了解自己，找到了真正与我能量共鸣的水晶。",
      "liWeiTestimonial": "东方玄学大师将传统智慧与现代洞察相结合。水晶推荐为我的日常生活带来了和谐。",
      
      // Birth Info Form
      "enterBirthInfo": "输入您的出生信息",
      "dateOfBirth": "出生日期",
      "birthDateEssential": "您的出生日期对于计算命运图表至关重要",
      "birthCountry": "出生国家",
      "birthCity": "出生城市（可选）",
      "birthCityPlaceholder": "搜索您的出生城市",
      "birthCityHelp": "添加出生城市有助于更准确的时区设置",
      "continue": "继续",
      "freeReadingsRemaining": "今日剩余免费解读次数：",
      
      // Loading Page
      "consultingStars": "占星问卜中...",
      "personalizedReading": "正在为您制作个性化的命运解读",
      "analyzingPositions": "分析您出生时刻的天体位置",
      "typicallyTakes": "通常需要20-40秒",
      "ancientWisdom": "古语云：\"耐心揭示智慧之路\"",
      "preparingReading": "正在准备您的个性化解读...",
      
      // Results Page
      "fateReading": "您的命运解读",
      "baziChart": "您的八字命盘",
      "fiveElementsProfile": "五行档案",
      "crystalJadeRecommendations": "水晶玉石推荐",
      "overallFate": "整体运势",
      "careerWealth": "事业财运",
      "relationships": "感情姻缘",
      "healthWellness": "健康养生",
      "unlockPremium": "解锁高级洞察",
      "premiumDescription": "获得深度命运分析，包含高级洞察和个性化指导。",
      "unlockWithCredits": "使用积分解锁",
      
      // Footer
      "secure": "安全",
      "private": "私密",
      "readings": "10K+ 解读",
      "allRightsReserved": "版权所有。",
      "discoverDestiny": "通过古老智慧探索您的命运",
      
      // Elements
      "wood": "木",
      "fire": "火",
      "earth": "土", 
      "metal": "金",
      "water": "水",
      
      // Auth
      "login": "登录",
      "register": "注册",
      "logout": "退出",
      "signOut": "退出登录",
      "email": "邮箱",
      "password": "密码",
      "confirmPassword": "确认密码",
      "name": "姓名",
      "enterEmail": "请输入邮箱",
      "enterPassword": "请输入密码",
      "enterName": "请输入姓名",
      "noAccount": "还没有账户？",
      "haveAccount": "已有账户？",
      "passwordsDoNotMatch": "密码不匹配",
      "loading": "加载中...",
      "continueWithGoogle": "使用 Google 继续",
      "continueWithGitHub": "使用 GitHub 继续",
      "continueWithDiscord": "使用 Discord 继续",
      "orContinueWith": "或使用邮箱继续",
      "completingSignIn": "正在完成登录",
      "pleaseWait": "请稍候，我们正在完成您的身份验证...",
      
      // Credits
      "credits": "积分",
      "availableCredits": "可用积分",
      "purchaseCredits": "购买积分",
      "creditHistory": "积分历史",
      "insufficientCredits": "积分不足",
      "basicReading": "基础解读",
      "basicReadingCost": "基础解读费用",
      "premiumReadingUnlock": "高级解读解锁",
      "creditConsumptionFailed": "积分消费失败",
      "unlockFailed": "解锁失败",
      "premiumContentLocked": "高级内容已锁定",
      "unlockPremiumDescription": "解锁所有人生方面的详细洞察",
      "mostPopular": "最受欢迎",
      "featured": "推荐",
      "validFor": "有效期",
      "days": "天",
      "purchase": "购买",
      "processing": "处理中...",
      "paymentMethods": "支付方式",
      "alipay": "支付宝",
      "wechatPay": "微信支付",
      "allTransactions": "全部交易",
      "purchases": "购买记录",
      "consumption": "消费记录",
      "bonuses": "奖励记录",
      "noTransactions": "暂无交易记录",
      "balance": "余额",
      "showing": "显示",
      "of": "共",
      "previous": "上一页",
      "next": "下一页",
      
      // Reading Generation
      "generating": "生成中...",
      "readingGenerationFailed": "解读生成失败",
      "creditsConsumed": "消费积分",
      "remainingCredits": "剩余积分",
      
      // Countries
      "china": "中国",
      "usa": "美国",
      "uk": "英国",
      "canada": "加拿大",
      "australia": "澳大利亚"
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    detection: {
      order: ['navigator', 'htmlTag', 'path', 'subdomain'],
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;