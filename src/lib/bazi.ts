import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ElementProfile, BirthInfo } from '../types/index';

// 天干
const HEAVENLY_STEMS = [
  '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'
];

// 地支
const EARTHLY_BRANCHES = [
  '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'
];

// 天干对应五行
const HEAVENLY_STEM_ELEMENTS: { [key: string]: string } = {
  '甲': 'Wood', '乙': 'Wood',
  '丙': 'Fire', '丁': 'Fire',
  '戊': 'Earth', '己': 'Earth',
  '庚': 'Metal', '辛': 'Metal',
  '壬': 'Water', '癸': 'Water'
};

// 地支对应五行
const EARTHLY_BRANCH_ELEMENTS: { [key: string]: string } = {
  '子': 'Water', '丑': 'Earth', '寅': 'Wood', '卯': 'Wood',
  '辰': 'Earth', '巳': 'Fire', '午': 'Fire', '未': 'Earth',
  '申': 'Metal', '酉': 'Metal', '戌': 'Earth', '亥': 'Water'
};

// 地支对应时辰
const HOUR_BRANCHES: { [key: string]: string } = {
  '子': '23:00-01:00', '丑': '01:00-03:00', '寅': '03:00-05:00', '卯': '05:00-07:00',
  '辰': '07:00-09:00', '巳': '09:00-11:00', '午': '11:00-13:00', '未': '13:00-15:00',
  '申': '15:00-17:00', '酉': '17:00-19:00', '戌': '19:00-21:00', '亥': '21:00-23:00'
};

// 月份对应地支
const MONTH_BRANCHES: { [key: number]: string } = {
  1: '寅', 2: '卯', 3: '辰', 4: '巳', 5: '午', 6: '未',
  7: '申', 8: '酉', 9: '戌', 10: '亥', 11: '子', 12: '丑'
};

class BaZiCalculator {
  // 计算某一年的天干地支
  private calculateYearPillar(year: number): { heavenly: string; earthly: string } {
    // 公历年份转换为干支纪年
    // 天干循环：(year - 4) % 10
    // 地支循环：(year - 4) % 12
    const heavenlyIndex = (year - 4) % 10;
    const earthlyIndex = (year - 4) % 12;
    
    return {
      heavenly: HEAVENLY_STEMS[heavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex]
    };
  }

  // 计算某一月的天干地支
  private calculateMonthPillar(year: number, month: number): { heavenly: string; earthly: string } {
    const earthly = MONTH_BRANCHES[month];
    
    // 月干计算公式：年干序数 * 2 + 月支序数
    const yearHeavenlyIndex = (year - 4) % 10;
    const monthEarthlyIndex = EARTHLY_BRANCHES.indexOf(earthly);
    const monthHeavenlyIndex = (yearHeavenlyIndex * 2 + monthEarthlyIndex) % 10;
    
    return {
      heavenly: HEAVENLY_STEMS[monthHeavenlyIndex],
      earthly: earthly
    };
  }

  // 计算某一日的天干地支
  private calculateDayPillar(date: Date): { heavenly: string; earthly: string } {
    // 简化版日干支计算（实际应该使用更复杂的万年历算法）
    // 这里使用一个基准日期进行计算
    const baseDate = new Date(1900, 0, 1); // 1900年1月1日
    const baseDayIndex = 0; // 假设基准日为甲子日
    
    const timeDiff = date.getTime() - baseDate.getTime();
    const dayDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    const heavenlyIndex = (baseDayIndex + dayDiff) % 10;
    const earthlyIndex = (baseDayIndex + dayDiff) % 12;
    
    return {
      heavenly: HEAVENLY_STEMS[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex]
    };
  }

  // 计算某一时的天干地支
  private calculateHourPillar(date: Date, dayHeavenly: string): { heavenly: string; earthly: string } {
    const hour = date.getHours();
    let earthlyIndex: number;
    
    // 根据小时确定地支
    if (hour >= 23 || hour < 1) earthlyIndex = 0; // 子时
    else if (hour >= 1 && hour < 3) earthlyIndex = 1; // 丑时
    else if (hour >= 3 && hour < 5) earthlyIndex = 2; // 寅时
    else if (hour >= 5 && hour < 7) earthlyIndex = 3; // 卯时
    else if (hour >= 7 && hour < 9) earthlyIndex = 4; // 辰时
    else if (hour >= 9 && hour < 11) earthlyIndex = 5; // 巳时
    else if (hour >= 11 && hour < 13) earthlyIndex = 6; // 午时
    else if (hour >= 13 && hour < 15) earthlyIndex = 7; // 未时
    else if (hour >= 15 && hour < 17) earthlyIndex = 8; // 申时
    else if (hour >= 17 && hour < 19) earthlyIndex = 9; // 酉时
    else if (hour >= 19 && hour < 21) earthlyIndex = 10; // 戌时
    else earthlyIndex = 11; // 亥时
    
    // 时干计算公式：日干序数 * 2 + 时支序数
    const dayHeavenlyIndex = HEAVENLY_STEMS.indexOf(dayHeavenly);
    const hourHeavenlyIndex = (dayHeavenlyIndex * 2 + earthlyIndex) % 10;
    
    return {
      heavenly: HEAVENLY_STEMS[hourHeavenlyIndex],
      earthly: EARTHLY_BRANCHES[earthlyIndex]
    };
  }

  // 主要计算方法
  calculateBaZi(birthInfo: BirthInfo): BaZiChart {
    const birthDate = new Date(birthInfo.date + 'T' + birthInfo.time);
    const year = birthDate.getFullYear();
    const month = birthDate.getMonth() + 1; // JavaScript月份从0开始
    
    const yearPillar = this.calculateYearPillar(year);
    const monthPillar = this.calculateMonthPillar(year, month);
    const dayPillar = this.calculateDayPillar(birthDate);
    const hourPillar = this.calculateHourPillar(birthDate, dayPillar.heavenly);
    
    return {
      year: {
        heavenly: yearPillar.heavenly,
        earthly: yearPillar.earthly,
        element: HEAVENLY_STEM_ELEMENTS[yearPillar.heavenly]
      },
      month: {
        heavenly: monthPillar.heavenly,
        earthly: monthPillar.earthly,
        element: HEAVENLY_STEM_ELEMENTS[monthPillar.heavenly]
      },
      day: {
        heavenly: dayPillar.heavenly,
        earthly: dayPillar.earthly,
        element: HEAVENLY_STEM_ELEMENTS[dayPillar.heavenly]
      },
      hour: {
        heavenly: hourPillar.heavenly,
        earthly: hourPillar.earthly,
        element: HEAVENLY_STEM_ELEMENTS[hourPillar.heavenly]
      }
    };
  }

  // 计算五行分布
  calculateElementProfile(chart: BaZiChart): ElementProfile {
    const elements = {
      Wood: 0,
      Fire: 0,
      Earth: 0,
      Metal: 0,
      Water: 0
    };

    // 统计天干五行
    elements[chart.year.element as keyof typeof elements] += 1;
    elements[chart.month.element as keyof typeof elements] += 1;
    elements[chart.day.element as keyof typeof elements] += 1;
    elements[chart.hour.element as keyof typeof elements] += 1;

    // 统计地支五行
    elements[EARTHLY_BRANCH_ELEMENTS[chart.year.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.month.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.day.earthly] as keyof typeof elements] += 1;
    elements[EARTHLY_BRANCH_ELEMENTS[chart.hour.earthly] as keyof typeof elements] += 1;

    // 计算百分比
    const total = 8; // 4个天干 + 4个地支
    return {
      wood: Math.round((elements.Wood / total) * 100),
      fire: Math.round((elements.Fire / total) * 100),
      earth: Math.round((elements.Earth / total) * 100),
      metal: Math.round((elements.Metal / total) * 100),
      water: Math.round((elements.Water / total) * 100)
    };
  }

  // 获取五行平衡建议
  getElementBalance(profile: ElementProfile): {
    strongest: string;
    weakest: string;
    balanced: boolean;
    suggestions: string[];
  } {
    const elements = [
      { name: 'wood', value: profile.wood, chinese: '木' },
      { name: 'fire', value: profile.fire, chinese: '火' },
      { name: 'earth', value: profile.earth, chinese: '土' },
      { name: 'metal', value: profile.metal, chinese: '金' },
      { name: 'water', value: profile.water, chinese: '水' }
    ];

    const strongest = elements.reduce((a, b) => a.value > b.value ? a : b);
    const weakest = elements.reduce((a, b) => a.value < b.value ? a : b);
    
    const balanced = Math.max(...elements.map(e => e.value)) - Math.min(...elements.map(e => e.value)) <= 30;
    
    const suggestions = [];
    if (!balanced) {
      if (weakest.value < 10) {
        suggestions.push(`需要补充${weakest.chinese}元素`);
      }
      if (strongest.value > 40) {
        suggestions.push(`需要化解${strongest.chinese}元素过旺`);
      }
    }

    return {
      strongest: strongest.chinese,
      weakest: weakest.chinese,
      balanced,
      suggestions
    };
  }
}

export const baziCalculator = new BaZiCalculator();