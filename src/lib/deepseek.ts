import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>P<PERSON><PERSON><PERSON>, DestinyAnalysis, ReadingContent } from '../types/index';

interface DeepSeekConfig {
  apiKey: string;
  baseUrl: string;
  maxTokens: number;
  temperature: number;
}

interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface DeepSeekError {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

class DeepSeekService {
  private config: DeepSeekConfig;

  constructor() {
    this.config = {
      apiKey: import.meta.env.VITE_DEEPSEEK_API_KEY || '',
      baseUrl: import.meta.env.VITE_DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
      maxTokens: 2000,
      temperature: 0.7
    };

    if (!this.config.apiKey) {
      throw new Error('DeepSeek API key is required');
    }
  }

  private async makeRequest(prompt: string): Promise<string> {
    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一位专业的八字命理大师，精通中国传统命理学。请根据用户提供的八字信息，提供准确、详细且有价值的命理分析。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      })
    });

    if (!response.ok) {
      const errorData: DeepSeekError = await response.json();
      throw new Error(`DeepSeek API Error: ${errorData.error.message}`);
    }

    const data: DeepSeekResponse = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  async analyzeDestiny(chart: BaZiChart, elementProfile: ElementProfile): Promise<DestinyAnalysis> {
    const prompt = `
请分析以下八字命盘：

年柱：${chart.year.heavenly}${chart.year.earthly}（${chart.year.element}）
月柱：${chart.month.heavenly}${chart.month.earthly}（${chart.month.element}）
日柱：${chart.day.heavenly}${chart.day.earthly}（${chart.day.element}）
时柱：${chart.hour.heavenly}${chart.hour.earthly}（${chart.hour.element}）

五行分布：
木：${elementProfile.wood}%
火：${elementProfile.fire}%
土：${elementProfile.earth}%
金：${elementProfile.metal}%
水：${elementProfile.water}%

请提供：
1. 整体命运分析
2. 五行平衡分析
3. 性格特点分析
4. 优势和挑战
5. 人生建议

请以JSON格式返回，包含以下字段：
{
  "overall": "整体分析内容",
  "elements": "五行分析内容",
  "personality": "性格分析内容",
  "strengths": "优势分析内容",
  "challenges": "挑战分析内容",
  "advice": "建议内容"
}
`;

    try {
      const response = await this.makeRequest(prompt);
      
      // 尝试解析JSON响应
      try {
        const analysis = JSON.parse(response);
        return {
          overall: analysis.overall || '整体分析暂不可用',
          elements: analysis.elements || '五行分析暂不可用',
          personality: analysis.personality || '性格分析暂不可用',
          strengths: analysis.strengths || '优势分析暂不可用',
          challenges: analysis.challenges || '挑战分析暂不可用',
          advice: analysis.advice || '建议暂不可用'
        };
      } catch (parseError) {
        // 如果JSON解析失败，将整个响应作为overall内容
        return {
          overall: response,
          elements: '五行分析暂不可用',
          personality: '性格分析暂不可用',
          strengths: '优势分析暂不可用',
          challenges: '挑战分析暂不可用',
          advice: '建议暂不可用'
        };
      }
    } catch (error) {
      console.error('DeepSeek API analysis error:', error);
      throw new Error('命理分析服务暂时不可用，请稍后重试');
    }
  }

  async generateReadings(analysis: DestinyAnalysis): Promise<ReadingContent> {
    const prompt = `
基于以下命理分析，请为用户生成详细的人生各方面解读：

整体分析：${analysis.overall}
五行分析：${analysis.elements}
性格分析：${analysis.personality}
优势：${analysis.strengths}
挑战：${analysis.challenges}
建议：${analysis.advice}

请提供以下方面的详细解读：
1. 事业运势
2. 感情运势
3. 健康运势
4. 财运分析
5. 学业/发展建议

请以JSON格式返回：
{
  "career": "事业运势详细解读",
  "relationships": "感情运势详细解读",
  "health": "健康运势详细解读",
  "wealth": "财运分析详细解读",
  "development": "学业发展建议"
}
`;

    try {
      const response = await this.makeRequest(prompt);
      
      try {
        const readings = JSON.parse(response);
        return {
          career: readings.career || '事业运势解读暂不可用',
          relationships: readings.relationships || '感情运势解读暂不可用',
          health: readings.health || '健康运势解读暂不可用',
          wealth: readings.wealth || '财运分析暂不可用',
          development: readings.development || '发展建议暂不可用'
        };
      } catch (parseError) {
        return {
          career: response,
          relationships: '感情运势解读暂不可用',
          health: '健康运势解读暂不可用',
          wealth: '财运分析暂不可用',
          development: '发展建议暂不可用'
        };
      }
    } catch (error) {
      console.error('DeepSeek API readings error:', error);
      throw new Error('详细解读服务暂时不可用，请稍后重试');
    }
  }

  async generateCrystalRecommendations(elementProfile: ElementProfile): Promise<Array<{
    name: string;
    nameZh: string;
    element: string;
    benefit: string;
    benefitZh: string;
    reason: string;
  }>> {
    const prompt = `
基于以下五行分布，请推荐适合的水晶：

五行分布：
木：${elementProfile.wood}%
火：${elementProfile.fire}%
土：${elementProfile.earth}%
金：${elementProfile.metal}%
水：${elementProfile.water}%

请根据五行缺失和过旺情况，推荐5-8种适合的水晶，每种水晶包含：
1. 英文名称
2. 中文名称
3. 对应五行
4. 功效（英文）
5. 功效（中文）
6. 推荐理由

请以JSON数组格式返回：
[
  {
    "name": "英文名称",
    "nameZh": "中文名称",
    "element": "对应五行",
    "benefit": "English benefit",
    "benefitZh": "中文功效",
    "reason": "推荐理由"
  }
]
`;

    try {
      const response = await this.makeRequest(prompt);
      
      try {
        const recommendations = JSON.parse(response);
        return Array.isArray(recommendations) ? recommendations : [];
      } catch (parseError) {
        // 如果解析失败，返回默认推荐
        return [
          {
            name: 'Clear Quartz',
            nameZh: '白水晶',
            element: 'Universal',
            benefit: 'Amplifies energy and brings clarity',
            benefitZh: '增强能量，带来清晰思维',
            reason: '适合平衡各种五行元素'
          }
        ];
      }
    } catch (error) {
      console.error('DeepSeek API crystal recommendations error:', error);
      // 返回默认推荐
      return [
        {
          name: 'Clear Quartz',
          nameZh: '白水晶',
          element: 'Universal',
          benefit: 'Amplifies energy and brings clarity',
          benefitZh: '增强能量，带来清晰思维',
          reason: '适合平衡各种五行元素'
        }
      ];
    }
  }
}

export const deepseekService = new DeepSeekService();