import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Lock, Users, Instagram, Facebook, Twitter, Pointer as Pinterest } from 'lucide-react';

export const Footer: React.FC = () => {
  const { t } = useTranslation();

  return (
    <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">東</span>
            </div>
            <span className="font-semibold text-gray-900 dark:text-white">Eastern Fate Master</span>
          </div>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('discoverDestiny')}
          </p>
          
          <div className="flex items-center justify-center space-x-8 mb-6">
            <div className="flex items-center space-x-2 text-purple-600 dark:text-purple-400">
              <Shield className="w-5 h-5" />
              <span className="text-sm font-medium">{t('secure')}</span>
            </div>
            <div className="flex items-center space-x-2 text-purple-600 dark:text-purple-400">
              <Lock className="w-5 h-5" />
              <span className="text-sm font-medium">{t('private')}</span>
            </div>
            <div className="flex items-center space-x-2 text-purple-600 dark:text-purple-400">
              <Users className="w-5 h-5" />
              <span className="text-sm font-medium">{t('readings')}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-4 mb-6">
            <a href="#" className="text-gray-400 hover:text-purple-500 transition-colors">
              <Instagram className="w-5 h-5" />
            </a>
            <a href="#" className="text-gray-400 hover:text-purple-500 transition-colors">
              <Facebook className="w-5 h-5" />
            </a>
            <a href="#" className="text-gray-400 hover:text-purple-500 transition-colors">
              <Twitter className="w-5 h-5" />
            </a>
            <a href="#" className="text-gray-400 hover:text-purple-500 transition-colors">
              <Pinterest className="w-5 h-5" />
            </a>
          </div>
          
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2025 Eastern Fate Master. {t('allRightsReserved')}
          </p>
        </div>
      </div>
    </footer>
  );
};