import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Coins, Star, Zap, Crown } from 'lucide-react';
import { useCreditPackages } from '../../hooks/useCredits';

interface CreditPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreditPurchaseModal: React.FC<CreditPurchaseModalProps> = ({
  isOpen,
  onClose
}) => {
  const { t } = useTranslation();
  const { packages, loading } = useCreditPackages();
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);

  const getPackageIcon = (type: string) => {
    switch (type) {
      case 'premium':
        return Crown;
      case 'promotional':
        return Zap;
      default:
        return Star;
    }
  };

  const handlePurchase = (packageId: string) => {
    setSelectedPackage(packageId);
    // TODO: Implement payment processing
    console.log('Purchase package:', packageId);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl mx-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <Coins className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {t('purchaseCredits')}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                </div>
              ) : (
                <div className="grid md:grid-cols-3 gap-6">
                  {packages.map((pkg) => {
                    const Icon = getPackageIcon(pkg.package_type);
                    const isPopular = pkg.is_popular;
                    const isFeatured = pkg.is_featured;
                    
                    return (
                      <motion.div
                        key={pkg.id}
                        whileHover={{ scale: 1.02 }}
                        className={`relative bg-white dark:bg-gray-800 rounded-xl p-6 border-2 transition-all duration-300 ${
                          isPopular 
                            ? 'border-purple-500 shadow-lg shadow-purple-500/20' 
                            : 'border-gray-200 dark:border-gray-700 hover:border-purple-300'
                        }`}
                      >
                        {isPopular && (
                          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                              {t('mostPopular')}
                            </span>
                          </div>
                        )}
                        
                        {isFeatured && (
                          <div className="absolute -top-3 right-4">
                            <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                              {t('featured')}
                            </span>
                          </div>
                        )}

                        <div className="text-center">
                          <div className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${
                            isPopular 
                              ? 'bg-gradient-to-r from-purple-500 to-pink-500' 
                              : 'bg-gray-100 dark:bg-gray-700'
                          }`}>
                            <Icon className={`w-6 h-6 ${
                              isPopular ? 'text-white' : 'text-gray-600 dark:text-gray-400'
                            }`} />
                          </div>
                          
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {pkg.name}
                          </h3>
                          
                          {pkg.description && (
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                              {pkg.description}
                            </p>
                          )}
                          
                          <div className="mb-4">
                            <div className="text-3xl font-bold text-gray-900 dark:text-white">
                              {pkg.credits}
                              {pkg.bonus_credits > 0 && (
                                <span className="text-lg text-purple-500">
                                  +{pkg.bonus_credits}
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {t('credits')}
                            </div>
                          </div>
                          
                          <div className="mb-6">
                            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                              ¥{pkg.price_cny || (pkg.price_usd * 7).toFixed(0)}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ${pkg.price_usd}
                            </div>
                          </div>
                          
                          {pkg.validity_days && (
                            <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                              {t('validFor')} {pkg.validity_days} {t('days')}
                            </div>
                          )}
                          
                          <button
                            onClick={() => handlePurchase(pkg.id)}
                            disabled={selectedPackage === pkg.id}
                            className={`w-full py-3 rounded-xl font-semibold transition-all duration-300 ${
                              isPopular
                                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg'
                                : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'
                            } disabled:opacity-50 disabled:cursor-not-allowed`}
                          >
                            {selectedPackage === pkg.id ? t('processing') : t('purchase')}
                          </button>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
              
              {/* Payment Methods */}
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('paymentMethods')}
                </h3>
                <div className="flex items-center justify-center space-x-6 text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">支</span>
                    </div>
                    <span className="text-sm">{t('alipay')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">微</span>
                    </div>
                    <span className="text-sm">{t('wechatPay')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-purple-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">S</span>
                    </div>
                    <span className="text-sm">Stripe</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};