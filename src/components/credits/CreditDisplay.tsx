import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Coins, Plus, History } from 'lucide-react';
import { useCredits } from '../../hooks/useCredits';

interface CreditDisplayProps {
  showActions?: boolean;
  onPurchase?: () => void;
  onHistory?: () => void;
}

export const CreditDisplay: React.FC<CreditDisplayProps> = ({
  showActions = true,
  onPurchase,
  onHistory
}) => {
  const { t } = useTranslation();
  const { credits, loading } = useCredits();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-4 text-white"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
            <Coins className="w-5 h-5" />
          </div>
          <div>
            <p className="text-white/80 text-sm">{t('availableCredits')}</p>
            <p className="text-2xl font-bold">
              {loading ? '...' : credits}
            </p>
          </div>
        </div>
        
        {showActions && (
          <div className="flex space-x-2">
            {onPurchase && (
              <button
                onClick={onPurchase}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                title={t('purchaseCredits')}
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
            {onHistory && (
              <button
                onClick={onHistory}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                title={t('creditHistory')}
              >
                <History className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};