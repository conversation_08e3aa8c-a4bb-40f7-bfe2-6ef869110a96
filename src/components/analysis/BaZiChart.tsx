import React from 'react';
import { motion } from 'framer-motion';
import { Star, Calendar, Clock, MapPin } from 'lucide-react';

interface BaZiPillar {
  heavenly: string;
  earthly: string;
  element: string;
}

interface BaZiChartData {
  year: BaZiPillar;
  month: BaZiPillar;
  day: BaZiPillar;
  hour: BaZiPillar;
}

interface BaZiChartProps {
  chart: BaZiChartData;
  birthInfo: {
    date: string;
    time: string;
    city: string;
    country: string;
  };
}

// 五行颜色映射
const elementColors = {
  '木': { bg: 'from-green-100 to-emerald-100', text: 'text-green-600', border: 'border-green-200' },
  '火': { bg: 'from-red-100 to-orange-100', text: 'text-red-600', border: 'border-red-200' },
  '土': { bg: 'from-yellow-100 to-amber-100', text: 'text-yellow-600', border: 'border-yellow-200' },
  '金': { bg: 'from-gray-100 to-slate-100', text: 'text-gray-600', border: 'border-gray-200' },
  '水': { bg: 'from-blue-100 to-cyan-100', text: 'text-blue-600', border: 'border-blue-200' },
};

// 暗色主题五行颜色
const elementColorsDark = {
  '木': { bg: 'from-green-900/30 to-emerald-900/30', text: 'text-green-400', border: 'border-green-700' },
  '火': { bg: 'from-red-900/30 to-orange-900/30', text: 'text-red-400', border: 'border-red-700' },
  '土': { bg: 'from-yellow-900/30 to-amber-900/30', text: 'text-yellow-400', border: 'border-yellow-700' },
  '金': { bg: 'from-gray-900/30 to-slate-900/30', text: 'text-gray-400', border: 'border-gray-700' },
  '水': { bg: 'from-blue-900/30 to-cyan-900/30', text: 'text-blue-400', border: 'border-blue-700' },
};

const PillarCard: React.FC<{
  pillar: BaZiPillar;
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  index: number;
}> = ({ pillar, title, subtitle, icon, index }) => {
  const colors = elementColors[pillar.element as keyof typeof elementColors] || elementColors['土'];
  const darkColors = elementColorsDark[pillar.element as keyof typeof elementColorsDark] || elementColorsDark['土'];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="relative"
    >
      {/* 柱子标题 */}
      <div className="text-center mb-3">
        <div className="flex items-center justify-center mb-1">
          {icon}
          <span className="ml-1 text-sm font-medium text-gray-700 dark:text-gray-300">
            {title}
          </span>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {subtitle}
        </div>
      </div>

      {/* 天干地支卡片 */}
      <div className={`
        relative bg-gradient-to-br ${colors.bg} dark:${darkColors.bg}
        border-2 ${colors.border} dark:${darkColors.border}
        rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300
        transform hover:-translate-y-1
      `}>
        {/* 天干 */}
        <div className="text-center mb-3">
          <div className={`text-3xl font-bold ${colors.text} dark:${darkColors.text} mb-1`}>
            {pillar.heavenly}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">
            天干
          </div>
        </div>

        {/* 分隔线 */}
        <div className={`h-px bg-gradient-to-r ${colors.bg} opacity-50 mb-3`}></div>

        {/* 地支 */}
        <div className="text-center">
          <div className={`text-2xl font-semibold ${colors.text} dark:${darkColors.text} mb-1`}>
            {pillar.earthly}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">
            地支
          </div>
        </div>

        {/* 五行标识 */}
        <div className="absolute -top-2 -right-2">
          <div className={`
            w-8 h-8 rounded-full bg-gradient-to-br ${colors.bg} dark:${darkColors.bg}
            border-2 ${colors.border} dark:${darkColors.border}
            flex items-center justify-center shadow-md
          `}>
            <span className={`text-xs font-bold ${colors.text} dark:${darkColors.text}`}>
              {pillar.element}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const BaZiChart: React.FC<BaZiChartProps> = ({ chart, birthInfo }) => {
  const pillars = [
    {
      data: chart.year,
      title: '年柱',
      subtitle: '祖上根基',
      icon: <Calendar className="w-4 h-4 text-purple-500" />
    },
    {
      data: chart.month,
      title: '月柱',
      subtitle: '父母宫',
      icon: <Calendar className="w-4 h-4 text-blue-500" />
    },
    {
      data: chart.day,
      title: '日柱',
      subtitle: '本命核心',
      icon: <Star className="w-4 h-4 text-yellow-500" />
    },
    {
      data: chart.hour,
      title: '时柱',
      subtitle: '子女宫',
      icon: <Clock className="w-4 h-4 text-green-500" />
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-white/20"
    >
      {/* 标题 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center justify-center">
          <Star className="w-6 h-6 mr-2 text-purple-500" />
          八字命盘
        </h2>
        <div className="flex items-center justify-center text-sm text-gray-600 dark:text-gray-400 space-x-4">
          <div className="flex items-center">
            <Calendar className="w-4 h-4 mr-1" />
            {birthInfo.date}
          </div>
          <div className="flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            {birthInfo.time}
          </div>
          <div className="flex items-center">
            <MapPin className="w-4 h-4 mr-1" />
            {birthInfo.city}, {birthInfo.country}
          </div>
        </div>
      </div>

      {/* 四柱展示 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {pillars.map((pillar, index) => (
          <PillarCard
            key={pillar.title}
            pillar={pillar.data}
            title={pillar.title}
            subtitle={pillar.subtitle}
            icon={pillar.icon}
            index={index}
          />
        ))}
      </div>

      {/* 说明文字 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="mt-8 p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-100 dark:border-purple-800"
      >
        <p className="text-sm text-gray-700 dark:text-gray-300 text-center">
          八字命盘由年、月、日、时四柱组成，每柱包含天干地支，反映出生时刻的天地能量状态，是命理分析的核心基础。
        </p>
      </motion.div>
    </motion.div>
  );
};
