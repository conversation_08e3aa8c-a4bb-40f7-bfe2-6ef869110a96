import React from 'react';
import { motion } from 'framer-motion';
import { Activity, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface ElementProfileData {
  wood: number;
  fire: number;
  earth: number;
  metal: number;
  water: number;
}

interface ElementProfileProps {
  elementProfile: ElementProfileData;
}

// 五行信息配置
const elementInfo = {
  wood: {
    name: '木',
    nameEn: 'Wood',
    color: 'green',
    description: '生长、创造、肝胆',
    icon: '🌱',
    gradient: 'from-green-400 to-emerald-500',
    bgGradient: 'from-green-50 to-emerald-50',
    darkBgGradient: 'from-green-900/20 to-emerald-900/20'
  },
  fire: {
    name: '火',
    nameEn: 'Fire',
    color: 'red',
    description: '热情、活力、心脏',
    icon: '🔥',
    gradient: 'from-red-400 to-orange-500',
    bgGradient: 'from-red-50 to-orange-50',
    darkBgGradient: 'from-red-900/20 to-orange-900/20'
  },
  earth: {
    name: '土',
    nameEn: 'Earth',
    color: 'yellow',
    description: '稳定、包容、脾胃',
    icon: '🏔️',
    gradient: 'from-yellow-400 to-amber-500',
    bgGradient: 'from-yellow-50 to-amber-50',
    darkBgGradient: 'from-yellow-900/20 to-amber-900/20'
  },
  metal: {
    name: '金',
    nameEn: 'Metal',
    color: 'gray',
    description: '理性、坚毅、肺部',
    icon: '⚡',
    gradient: 'from-gray-400 to-slate-500',
    bgGradient: 'from-gray-50 to-slate-50',
    darkBgGradient: 'from-gray-900/20 to-slate-900/20'
  },
  water: {
    name: '水',
    nameEn: 'Water',
    color: 'blue',
    description: '智慧、流动、肾脏',
    icon: '💧',
    gradient: 'from-blue-400 to-cyan-500',
    bgGradient: 'from-blue-50 to-cyan-50',
    darkBgGradient: 'from-blue-900/20 to-cyan-900/20'
  }
};

const ElementBar: React.FC<{
  element: keyof typeof elementInfo;
  percentage: number;
  index: number;
}> = ({ element, percentage, index }) => {
  const info = elementInfo[element];
  
  // 判断强弱
  const getStrengthIcon = (percentage: number) => {
    if (percentage > 25) return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (percentage < 15) return <TrendingDown className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getStrengthText = (percentage: number) => {
    if (percentage > 25) return '偏旺';
    if (percentage < 15) return '偏弱';
    return '平衡';
  };

  const getStrengthColor = (percentage: number) => {
    if (percentage > 25) return 'text-green-600 dark:text-green-400';
    if (percentage < 15) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1 }}
      className={`
        p-4 rounded-xl bg-gradient-to-r ${info.bgGradient} dark:${info.darkBgGradient}
        border border-white/50 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-300
      `}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">{info.icon}</div>
          <div>
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-gray-900 dark:text-white">
                {info.name}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {info.nameEn}
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              {info.description}
            </div>
          </div>
        </div>
        
        <div className="text-right">
          <div className="flex items-center space-x-1 mb-1">
            {getStrengthIcon(percentage)}
            <span className={`text-sm font-medium ${getStrengthColor(percentage)}`}>
              {getStrengthText(percentage)}
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {percentage}%
          </div>
        </div>
      </div>

      {/* 进度条 */}
      <div className="relative">
        <div className="h-3 bg-white/50 dark:bg-gray-700/50 rounded-full overflow-hidden">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ delay: index * 0.1 + 0.3, duration: 0.8, ease: "easeOut" }}
            className={`h-full bg-gradient-to-r ${info.gradient} rounded-full relative`}
          >
            {/* 光泽效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          </motion.div>
        </div>
        
        {/* 刻度线 */}
        <div className="absolute top-0 left-1/4 w-px h-3 bg-gray-300 dark:bg-gray-600"></div>
        <div className="absolute top-0 left-1/2 w-px h-3 bg-gray-300 dark:bg-gray-600"></div>
        <div className="absolute top-0 left-3/4 w-px h-3 bg-gray-300 dark:bg-gray-600"></div>
      </div>
    </motion.div>
  );
};

const ElementRadarChart: React.FC<{ elementProfile: ElementProfileData }> = ({ elementProfile }) => {
  const elements = Object.entries(elementProfile);
  const centerX = 120;
  const centerY = 120;
  const radius = 80;

  // 计算五边形的点
  const points = elements.map((_, index) => {
    const angle = (index * 2 * Math.PI) / 5 - Math.PI / 2;
    return {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle)
    };
  });

  // 计算数据点
  const dataPoints = elements.map(([_, percentage], index) => {
    const angle = (index * 2 * Math.PI) / 5 - Math.PI / 2;
    const dataRadius = (percentage / 100) * radius;
    return {
      x: centerX + dataRadius * Math.cos(angle),
      y: centerY + dataRadius * Math.sin(angle)
    };
  });

  return (
    <div className="flex justify-center">
      <svg width="240" height="240" className="drop-shadow-lg">
        {/* 背景网格 */}
        {[0.2, 0.4, 0.6, 0.8, 1].map((scale, index) => (
          <polygon
            key={index}
            points={points.map(p => `${centerX + (p.x - centerX) * scale},${centerY + (p.y - centerY) * scale}`).join(' ')}
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            className="text-gray-200 dark:text-gray-700"
            opacity={0.3}
          />
        ))}

        {/* 轴线 */}
        {points.map((point, index) => (
          <line
            key={index}
            x1={centerX}
            y1={centerY}
            x2={point.x}
            y2={point.y}
            stroke="currentColor"
            strokeWidth="1"
            className="text-gray-300 dark:text-gray-600"
            opacity={0.5}
          />
        ))}

        {/* 数据区域 */}
        <motion.polygon
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          points={dataPoints.map(p => `${p.x},${p.y}`).join(' ')}
          fill="url(#elementGradient)"
          stroke="#8B5CF6"
          strokeWidth="2"
          opacity={0.7}
        />

        {/* 数据点 */}
        {dataPoints.map((point, index) => (
          <motion.circle
            key={index}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.7 + index * 0.1 }}
            cx={point.x}
            cy={point.y}
            r="4"
            fill="#8B5CF6"
            stroke="white"
            strokeWidth="2"
          />
        ))}

        {/* 标签 */}
        {elements.map(([element, _], index) => {
          const angle = (index * 2 * Math.PI) / 5 - Math.PI / 2;
          const labelRadius = radius + 20;
          const labelX = centerX + labelRadius * Math.cos(angle);
          const labelY = centerY + labelRadius * Math.sin(angle);
          
          return (
            <text
              key={element}
              x={labelX}
              y={labelY}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-sm font-medium fill-gray-700 dark:fill-gray-300"
            >
              {elementInfo[element as keyof typeof elementInfo].name}
            </text>
          );
        })}

        {/* 渐变定义 */}
        <defs>
          <radialGradient id="elementGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#EC4899" stopOpacity="0.3" />
          </radialGradient>
        </defs>
      </svg>
    </div>
  );
};

export const ElementProfile: React.FC<ElementProfileProps> = ({ elementProfile }) => {
  const elements = Object.entries(elementProfile) as [keyof typeof elementInfo, number][];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-white/20"
    >
      {/* 标题 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center justify-center">
          <Activity className="w-6 h-6 mr-2 text-purple-500" />
          五行分析
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          五行平衡反映身心健康状态和性格特质
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 雷达图 */}
        <div className="flex flex-col items-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            五行平衡图
          </h3>
          <ElementRadarChart elementProfile={elementProfile} />
        </div>

        {/* 详细数据 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            详细分析
          </h3>
          {elements.map(([element, percentage], index) => (
            <ElementBar
              key={element}
              element={element}
              percentage={percentage}
              index={index}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};
