import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Shield, Lock, Users } from 'lucide-react';
import { analysisService } from '../lib/analysis';
import { BirthInfo } from '../types/index';

export const LoadingPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [status, setStatus] = useState<string>('开始分析...');
  const [error, setError] = useState<string | null>(null);

  const steps = [
    { icon: '🔥', color: 'from-red-500 to-orange-500', name: '火' },
    { icon: '💧', color: 'from-blue-500 to-cyan-500', name: '水' },
    { icon: '⚡', color: 'from-yellow-500 to-amber-500', name: '金' },
    { icon: '🌍', color: 'from-green-500 to-emerald-500', name: '土' },
    { icon: '🌲', color: 'from-green-600 to-green-400', name: '木' }
  ];

  useEffect(() => {
    const performAnalysis = async () => {
      try {
        // 获取用户输入的生辰信息
        const birthInfoStr = localStorage.getItem('birthInfo');
        if (!birthInfoStr) {
          setError('缺少生辰信息，请重新输入');
          return;
        }

        const storedBirthInfo = JSON.parse(birthInfoStr);

        // 转换为后端 API 需要的格式
        const birthInfo = {
          date: storedBirthInfo.date,
          time: storedBirthInfo.time,
          location: `${storedBirthInfo.city}, ${storedBirthInfo.country}`,
          timezone: 'Asia/Shanghai', // 默认时区
          gender: 'male' // 默认性别，后续可以让用户选择
        };

        // 开始分析过程
        const analysisSteps = [
          { progress: 20, status: '计算八字命盘...' },
          { progress: 40, status: '分析五行分布...' },
          { progress: 60, status: '生成命理解读...' },
          { progress: 80, status: '推荐水晶配饰...' },
          { progress: 100, status: '分析完成！' }
        ];

        // 模拟分析过程
        for (let i = 0; i < analysisSteps.length; i++) {
          const step = analysisSteps[i];
          setProgress(step.progress);
          setStatus(step.status);

          if (i === analysisSteps.length - 1) {
            // 最后一步：调用后端分析 API
            const response = await fetch('http://localhost:8787/v1/readings', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer dev-user-123` // 开发环境使用固定令牌
              },
              body: JSON.stringify({ birthInfo })
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              throw new Error(result.error?.message || '分析请求失败');
            }

            // 保存分析结果到本地存储
            const analysisResult = {
              birthInfo: storedBirthInfo,
              baziChart: result.data.bazi_chart,
              elementProfile: result.data.element_profile,
              destinyAnalysis: result.data.destiny_analysis,
              readingContent: result.data.reading_content,
              crystalRecommendations: result.data.crystal_recommendations,
              balance: {
                strongest: 'Unknown',
                weakest: 'Unknown',
                balanced: false,
                suggestions: []
              }
            };

            localStorage.setItem('analysisResult', JSON.stringify(analysisResult));

            // 等待一秒后跳转到结果页面
            setTimeout(() => navigate('/results'), 1000);
          } else {
            // 其他步骤：等待一段时间
            await new Promise(resolve => setTimeout(resolve, 1500));
          }
        }
      } catch (error) {
        console.error('Analysis error:', error);
        setError(error instanceof Error ? error.message : '分析过程中发生错误');
      }
    };

    performAnalysis();
  }, [navigate]);

  useEffect(() => {
    const stepTimer = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % steps.length);
    }, 800);

    return () => clearInterval(stepTimer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-indigo-900 to-purple-800 flex items-center justify-center relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      <div className="max-w-2xl mx-auto px-4 text-center relative z-10">
        {/* Logo */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200 }}
          className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-8"
        >
          <span className="text-white font-bold text-3xl">東</span>
        </motion.div>

        {/* Title */}
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-4xl font-bold text-white mb-4"
        >
          {t('consultingStars')}
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-xl text-purple-200 mb-12"
        >
          {t('personalizedReading')}
        </motion.p>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="w-full bg-purple-800/50 rounded-full h-3 mb-4">
            <motion.div
              className="bg-gradient-to-r from-purple-400 to-pink-400 h-3 rounded-full"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.2 }}
            />
          </div>
          <p className="text-purple-200 text-sm">
            {error ? error : status}
          </p>
        </div>

        {/* Animated Elements */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl ${
                index === currentStep ? 'bg-gradient-to-br ' + step.color : 'bg-purple-800/30'
              }`}
              animate={{
                scale: index === currentStep ? 1.2 : 1,
                opacity: index === currentStep ? 1 : 0.5,
              }}
              transition={{ duration: 0.3 }}
            >
              {step.icon}
            </motion.div>
          ))}
        </div>

        {/* Error or Time Indicator */}
        {error ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-red-300 mb-4"
          >
            <p className="mb-4">{error}</p>
            <button
              onClick={() => navigate('/birth-info')}
              className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              返回重试
            </button>
          </motion.div>
        ) : (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="text-purple-300 mb-4"
          >
            {t('typicallyTakes')}
          </motion.p>
        )}

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-purple-200 italic mb-12"
        >
          {t('ancientWisdom')}
        </motion.p>

        {/* Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="bg-purple-800/50 backdrop-blur-md rounded-2xl p-4 inline-flex items-center space-x-3"
        >
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
          <span className="text-white font-medium">{t('preparingReading')}</span>
        </motion.div>
      </div>

      {/* Trust Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex items-center space-x-8 text-purple-300">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span className="text-sm font-medium">{t('secure')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Lock className="w-5 h-5" />
            <span className="text-sm font-medium">{t('private')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span className="text-sm font-medium">{t('readings')}</span>
          </div>
        </div>
        
        <div className="text-center mt-4">
          <p className="text-sm text-purple-400">
            {t('freeReadingsRemaining')} <span className="font-bold text-purple-300">1</span>
          </p>
        </div>
        
        <div className="text-center mt-2">
          <p className="text-xs text-purple-500">
            © 2025 Eastern Fate Master. {t('allRightsReserved')}
          </p>
        </div>
      </div>
    </div>
  );
};