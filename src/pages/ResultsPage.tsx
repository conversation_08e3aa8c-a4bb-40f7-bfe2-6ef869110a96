import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Star, 
  Heart, 
  Briefcase, 
  Activity,
  Sparkles,
  Lock,
  Share2,
  Download
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useCredits } from '../hooks/useCredits';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { CreditPurchaseModal } from '../components/credits/CreditPurchaseModal';
import { analysisService, CompleteAnalysis } from '../lib/analysis';
import { BaZiChart } from '../components/analysis/BaZiChart';
import { ElementProfile } from '../components/analysis/ElementProfile';

export const ResultsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { credits, hasEnoughCredits, consumeCredits } = useCredits();
  const [activeTab, setActiveTab] = useState('overall');
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [unlockedTabs, setUnlockedTabs] = useState<string[]>(['overall']);
  const [analysis, setAnalysis] = useState<CompleteAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载分析结果
  useEffect(() => {
    const loadAnalysis = () => {
      try {
        const analysisResult = analysisService.getAnalysisFromStorage();
        if (analysisResult) {
          setAnalysis(analysisResult);
        } else {
          setError('分析结果未找到，请重新进行分析');
        }
      } catch (err) {
        setError('加载分析结果时发生错误');
      } finally {
        setLoading(false);
      }
    };

    loadAnalysis();
  }, []);

  const tabs = [
    { id: 'overall', label: t('overallFate'), icon: Star },
    { id: 'career', label: t('careerWealth'), icon: Briefcase },
    { id: 'relationships', label: t('relationships'), icon: Heart },
    { id: 'health', label: t('healthWellness'), icon: Activity }
  ];

  // 显示加载状态
  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900 pt-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">加载分析结果中...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // 显示错误状态
  if (error || !analysis) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900 pt-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
            <div className="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
              <p>{error || '分析结果加载失败'}</p>
            </div>
            <button
              onClick={() => navigate('/birth-info')}
              className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              重新开始分析
            </button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const handleUnlockPremium = async () => {
    if (!hasEnoughCredits(15)) {
      setShowPurchaseModal(true);
      return;
    }
    
    const result = await consumeCredits(15, 'premium_reading', undefined, t('premiumReadingUnlock'));
    
    if (result.success) {
      setUnlockedTabs(['overall', 'career', 'relationships', 'health']);
    } else {
      alert(result.error || t('unlockFailed'));
    }
  };

  const isTabLocked = (tabId: string) => {
    return !unlockedTabs.includes(tabId);
  };
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900 pt-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">東</span>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            {t('fateReading')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {user?.name || user?.email} • Born: {analysis.birthInfo.date} • {analysis.birthInfo.country}
          </p>
        </motion.div>

        {/* BaZi Chart */}
        <BaZiChart chart={analysis.baziChart} birthInfo={analysis.birthInfo} />

        {/* Five Elements Profile */}
        <ElementProfile elementProfile={analysis.elementProfile} />

        {/* Crystal Recommendations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl p-6 shadow-lg border border-white/20 mb-8"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
            {t('crystalJadeRecommendations')}
          </h2>
          
          <div className="grid md:grid-cols-3 gap-4">
            {analysis.crystalRecommendations.map((crystal, index) => (
              <div key={index} className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-3">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {crystal.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {crystal.nameZh} • {crystal.element}
                </p>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {crystal.benefitZh}
                </p>
                {crystal.reason && (
                  <p className="text-xs text-purple-600 dark:text-purple-400 mt-2">
                    {crystal.reason}
                  </p>
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Reading Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 mb-8"
        >
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200 dark:border-gray-700">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  if (isTabLocked(tab.id)) {
                    handleUnlockPremium();
                  } else {
                    setActiveTab(tab.id);
                  }
                }}
                className={`flex-1 flex items-center justify-center space-x-2 py-4 px-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-purple-600 dark:text-purple-400 border-b-2 border-purple-500'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.label}</span>
                {isTabLocked(tab.id) && <Lock className="w-3 h-3" />}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {isTabLocked(activeTab) ? (
              <div className="text-center py-8">
                <Lock className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {t('premiumContentLocked')}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {t('unlockPremiumDescription')}
                </p>
                <button
                  onClick={handleUnlockPremium}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
                >
                  {t('unlockWithCredits')} (15 {t('credits')})
                </button>
              </div>
            ) : (
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {activeTab === 'overall' && (
                    <div>
                      <h3 className="font-semibold mb-2">整体分析</h3>
                      <p className="mb-4">{analysis.destinyAnalysis.overall}</p>
                      <h3 className="font-semibold mb-2">性格特点</h3>
                      <p className="mb-4">{analysis.destinyAnalysis.personality}</p>
                      <h3 className="font-semibold mb-2">建议</h3>
                      <p>{analysis.destinyAnalysis.advice}</p>
                    </div>
                  )}
                  {activeTab === 'career' && (
                    <p>{analysis.readingContent.career}</p>
                  )}
                  {activeTab === 'relationships' && (
                    <p>{analysis.readingContent.relationships}</p>
                  )}
                  {activeTab === 'health' && (
                    <p>{analysis.readingContent.health}</p>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Premium Unlock - Only show if not all tabs are unlocked */}
        {unlockedTabs.length < tabs.length && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-6 text-white text-center mb-8"
          >
            <Lock className="w-12 h-12 mx-auto mb-4 opacity-80" />
            <h3 className="text-xl font-semibold mb-2">{t('unlockPremium')}</h3>
            <p className="mb-4 opacity-90">{t('premiumDescription')}</p>
            <button 
              onClick={handleUnlockPremium}
              className="bg-white text-purple-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
            >
              {t('unlockWithCredits')} (15 {t('credits')})
            </button>
          </motion.div>
        )}

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="flex flex-col sm:flex-row gap-4 mb-8"
        >
          <button className="flex-1 bg-purple-500 text-white py-3 px-6 rounded-xl font-semibold hover:bg-purple-600 transition-colors flex items-center justify-center space-x-2">
            <Share2 className="w-5 h-5" />
            <span>分享解读</span>
          </button>
          <button className="flex-1 bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400 py-3 px-6 rounded-xl font-semibold border border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors flex items-center justify-center space-x-2">
            <Download className="w-5 h-5" />
            <span>保存报告</span>
          </button>
        </motion.div>
      </div>
      </div>
      
      <CreditPurchaseModal 
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
      />
    </ProtectedRoute>
  );
};