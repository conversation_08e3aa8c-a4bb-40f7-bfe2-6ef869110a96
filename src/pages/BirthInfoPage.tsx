import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, MapPin, ArrowRight, Shield, Lock, Users } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useCredits } from '../hooks/useCredits';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';

export const BirthInfoPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { credits, hasEnoughCredits, consumeCredits } = useCredits();
  
  const [formData, setFormData] = useState({
    birthDate: '',
    birthTime: '12:00',
    birthCountry: 'China',
    birthCity: ''
  });

  const countries = [
    { value: 'China', label: t('china') },
    { value: 'USA', label: t('usa') },
    { value: 'UK', label: t('uk') },
    { value: 'Canada', label: t('canada') },
    { value: 'Australia', label: t('australia') }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.birthDate && formData.birthTime && formData.birthCountry) {
      // Check if user has enough credits
      if (!hasEnoughCredits(10)) {
        alert(t('insufficientCredits'));
        return;
      }
      
      // Consume credits for basic reading
      const result = await consumeCredits(10, 'reading', undefined, t('basicReading'));
      
      if (!result.success) {
        alert(result.error || t('creditConsumptionFailed'));
        return;
      }
      
      // Store form data in localStorage for the loading page
      const birthInfo = {
        date: formData.birthDate,
        time: formData.birthTime,
        location: `${formData.birthCity}, ${formData.birthCountry}`, // 统一使用 location 字段
        timezone: 'Asia/Shanghai', // 默认时区，后续可以根据地点自动检测
        gender: 'male' as const, // 默认性别，后续可以添加性别选择
        // 为了向后兼容，保留原有字段
        country: formData.birthCountry,
        city: formData.birthCity
      };
      localStorage.setItem('birthInfo', JSON.stringify(birthInfo));
      navigate('/loading');
    }
  };

  const steps = [
    { number: 1, active: true, icon: Calendar },
    { number: 2, active: false, icon: MapPin },
    { number: 3, active: false, icon: ArrowRight }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900 pt-24">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20"
        >
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold text-2xl">東</span>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              Eastern Fate Master
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {t('enterBirthInfo')}
            </p>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            {steps.map((step, index) => (
              <React.Fragment key={step.number}>
                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                  step.active 
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' 
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-400'
                }`}>
                  <step.icon className="w-5 h-5" />
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    step.active ? 'bg-gradient-to-r from-purple-500 to-pink-500' : 'bg-gray-200 dark:bg-gray-700'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Birth Date */}
            <div>
              <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                {t('dateOfBirth')}
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={formData.birthDate}
                  onChange={(e) => setFormData({ ...formData, birthDate: e.target.value })}
                  className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  required
                />
                <Calendar className="absolute right-3 top-3 w-5 h-5 text-purple-500 pointer-events-none" />
              </div>
              <p className="text-sm text-purple-500 dark:text-purple-400 mt-1">
                {t('birthDateEssential')}
              </p>
            </div>

            {/* Birth Time */}
            <div>
              <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                {t('timeOfBirth')}
              </label>
              <div className="relative">
                <input
                  type="time"
                  value={formData.birthTime}
                  onChange={(e) => setFormData({ ...formData, birthTime: e.target.value })}
                  className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  required
                />
              </div>
              <p className="text-sm text-purple-500 dark:text-purple-400 mt-1">
                {t('birthTimeEssential')}
              </p>
            </div>

            {/* Birth Country */}
            <div>
              <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                {t('birthCountry')}
              </label>
              <select
                value={formData.birthCountry}
                onChange={(e) => setFormData({ ...formData, birthCountry: e.target.value })}
                className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                required
              >
                {countries.map((country) => (
                  <option key={country.value} value={country.value}>
                    {country.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Birth City */}
            <div>
              <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                {t('birthCity')}
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.birthCity}
                  onChange={(e) => setFormData({ ...formData, birthCity: e.target.value })}
                  placeholder={t('birthCityPlaceholder')}
                  className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                />
                <MapPin className="absolute right-3 top-3 w-5 h-5 text-purple-500 pointer-events-none" />
              </div>
              <p className="text-sm text-purple-500 dark:text-purple-400 mt-1">
                {t('birthCityHelp')}
              </p>
            </div>

            {/* Submit Button */}
            <motion.button
              type="submit"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
            >
              <span>{t('continue')}</span>
              <ArrowRight className="w-5 h-5" />
            </motion.button>

            {/* Free Readings Counter */}
            <div className="text-center">
              <p className="text-sm text-purple-600 dark:text-purple-400">
                {t('availableCredits')}: <span className="font-bold text-purple-600">{credits}</span>
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t('basicReadingCost')}: 10 {t('credits')}
              </p>
            </div>
          </form>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="flex items-center justify-center space-x-8 mt-8 text-purple-600 dark:text-purple-400"
        >
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span className="text-sm font-medium">{t('secure')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Lock className="w-5 h-5" />
            <span className="text-sm font-medium">{t('private')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span className="text-sm font-medium">{t('readings')}</span>
          </div>
        </motion.div>

        <div className="text-center mt-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2025 Eastern Fate Master. {t('allRightsReserved')}
          </p>
        </div>
      </div>
      </div>
    </ProtectedRoute>
  );
};