export interface User {
  id: string;
  email: string;
  points: number;
  freeReadingsToday: number;
  createdAt: string;
}

export interface BaZiReading {
  id: string;
  userId: string;
  birthDate: string;
  birthCountry: string;
  birthCity?: string;
  reading: string;
  type: 'basic' | 'premium';
  createdAt: string;
}

export interface Invitation {
  id: string;
  inviterId: string;
  inviteeEmail: string;
  status: 'pending' | 'completed';
  pointsAwarded: number;
  createdAt: string;
}

export interface ReadingStep {
  id: number;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
}

// 八字相关类型定义
export interface BaZiChart {
  year: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  month: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  day: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  hour: {
    heavenly: string;
    earthly: string;
    element: string;
  };
}

export interface ElementProfile {
  wood: number;
  fire: number;
  earth: number;
  metal: number;
  water: number;
}

export interface DestinyAnalysis {
  overall: string;
  elements: string;
  personality: string;
  strengths: string;
  challenges: string;
  advice: string;
}

export interface ReadingContent {
  career: string;
  relationships: string;
  health: string;
  wealth: string;
  development: string;
}

export interface CrystalRecommendation {
  name: string;
  nameZh: string;
  element: string;
  benefit: string;
  benefitZh: string;
  reason: string;
}

export interface BirthInfo {
  date: string;
  time: string;
  country: string;
  city: string;
  timezone?: string;
}