export interface User {
  id: string;
  clerk_user_id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  credits: number;                    // 统一使用 credits 而不是 points
  language_preference: string;
  timezone: string;
  subscription_status: string;
  total_readings: number;
  last_reading_at: string | null;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface BaZiReading {
  id: string;
  userId: string;
  birthDate: string;
  birthCountry: string;
  birthCity?: string;
  reading: string;
  type: 'basic' | 'premium';
  createdAt: string;
}

export interface Invitation {
  id: string;
  inviterId: string;
  inviteeEmail: string;
  status: 'pending' | 'completed';
  pointsAwarded: number;
  createdAt: string;
}

export interface ReadingStep {
  id: number;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
}

// 八字相关类型定义
export interface BaZiChart {
  year: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  month: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  day: {
    heavenly: string;
    earthly: string;
    element: string;
  };
  hour: {
    heavenly: string;
    earthly: string;
    element: string;
  };
}

export interface ElementProfile {
  wood: number;
  fire: number;
  earth: number;
  metal: number;
  water: number;
}

export interface DestinyAnalysis {
  overall: string;
  elements: string;
  personality: string;
  strengths: string;
  challenges: string;
  advice: string;
}

export interface ReadingContent {
  career: string;
  relationships: string;
  health: string;
  wealth: string;
  development: string;
}

export interface CrystalRecommendation {
  name: string;
  nameZh: string;
  element: string;
  benefit: string;
  benefitZh: string;
  reason: string;
}

export interface BirthInfo {
  date: string;
  time: string;
  location: string;                   // 统一使用 location 而不是 country + city
  timezone: string;
  gender: 'male' | 'female';
}

// 为了向后兼容，保留旧的接口但标记为废弃
/** @deprecated 使用 BirthInfo 代替 */
export interface LegacyBirthInfo {
  date: string;
  time: string;
  country: string;
  city: string;
  timezone?: string;
}

// 积分相关类型
export interface CreditTransaction {
  id: string;
  user_id: string;
  type: 'purchase' | 'consume' | 'refund' | 'bonus' | 'admin_adjust' | 'expire';
  amount: number;
  balance_before?: number;
  balance_after?: number;
  description: string;
  reference_id?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  name_zh?: string;
  description?: string;
  credits: number;
  price_usd?: number;
  price_cny?: number;
  currency: string;
  bonus_credits: number;
  validity_days: number;
  package_type: 'standard' | 'premium' | 'promotional';
  display_order: number;
  is_popular: boolean;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 完整分析结果类型
export interface CompleteAnalysis {
  birthInfo: BirthInfo;
  baziChart: BaZiChart;
  elementProfile: ElementProfile;
  destinyAnalysis: DestinyAnalysis;
  readingContent: ReadingContent;
  crystalRecommendations: CrystalRecommendation[];
}