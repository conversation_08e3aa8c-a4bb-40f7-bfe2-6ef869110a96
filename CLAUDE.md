# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build production bundle
- `npm run lint` - Run ESLint to check code quality
- `npm run preview` - Preview production build locally

### Environment Setup
- Copy `.env.example` to `.env` and configure:
  - `VITE_SUPABASE_URL` - Supabase project URL
  - `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key

## Architecture Overview

### Tech Stack
- **Frontend**: React 18 + TypeScript with Vite
- **Styling**: Tailwind CSS with dark mode support
- **Backend**: Supabase (PostgreSQL database + authentication)
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Internationalization**: react-i18next (English/Chinese)
- **Routing**: React Router DOM

### Core Application Structure

#### Authentication System
- `src/lib/auth.ts` - AuthService class with OAuth providers (Google, GitHub, Discord, Facebook, Twitter)
- `src/hooks/useAuth.ts` - React hook for authentication state management
- `src/components/auth/` - Authentication components (LoginModal, ProtectedRoute)
- `src/pages/AuthCallback.tsx` - OAuth callback handler

#### Credit System
- `src/lib/credits.ts` - CreditService class managing user credits and transactions
- `src/hooks/useCredits.ts` - React hook for credit operations
- `src/components/credits/` - Credit-related UI components
- Transaction types: purchase, consume, refund, bonus, admin_adjust, expire

#### Page Flow
1. `HomePage.tsx` - Landing page with features showcase
2. `BirthInfoPage.tsx` - Birth information form with validation
3. `LoadingPage.tsx` - Analysis progress with five elements visualization
4. `ResultsPage.tsx` - BaZi reading results with premium content system

#### Database Schema (Supabase)
- `users` table with profile data, credits, subscription status
- `credit_transactions` table for transaction history
- `credit_packages` table for purchasable credit packages
- User authentication handled by Supabase Auth

#### Key Features
- **Dark Mode**: Persistent theme toggle stored in localStorage
- **Internationalization**: Auto-detect browser language, switchable between English/Chinese
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Premium Content**: Credit-based system for advanced readings
- **OAuth Integration**: Multiple social login providers

### Important Patterns

#### State Management
- React hooks for local state
- Supabase real-time subscriptions for auth changes
- Custom hooks (`useAuth`, `useCredits`) for business logic

#### API Integration
- Supabase client configured in `src/lib/supabase.ts`
- Service classes pattern for backend interactions
- Error handling with try-catch and user-friendly messages

#### Component Architecture
- Page components in `src/pages/`
- Reusable UI components in `src/components/`
- Custom hooks in `src/hooks/`
- Utility functions in `src/lib/`

#### TypeScript Usage
- Strict typing enabled
- Custom interfaces in `src/types/index.ts`
- Service classes with proper type definitions
- Supabase-generated types in `supabase/types.ts`

## Development Notes

### Supabase Integration
- Database migrations in `supabase/migrations/`
- Row Level Security (RLS) policies required for data access
- Real-time subscriptions for auth state changes

### Build Configuration
- Vite with React plugin
- ESLint with TypeScript and React hooks rules
- Tailwind CSS with PostCSS for styling
- Lucide React icons excluded from optimization

### Deployment
- Designed for Cloudflare Pages deployment
- Static site generation with Vite
- Environment variables configured in deployment platform