/*
  # 创建 users 表 - 数据库代码一致性修复

  1. 问题
    - 代码中大量使用 users 表，但数据库中缺失该表定义
    - 用户注册/登录功能完全无法工作
    - 积分系统无法关联用户

  2. 解决方案
    - 根据代码中的实际使用创建 users 表
    - 包含所有代码期望的字段
    - 建立正确的索引和约束
    - 设置合适的 RLS 策略

  3. 字段说明
    - id: 主键，UUID 类型
    - clerk_user_id: 与 Supabase Auth 关联的外部用户ID
    - email: 用户邮箱，唯一约束
    - name: 用户姓名
    - avatar_url: 头像URL
    - credits: 用户积分余额（统一积分存储）
    - language_preference: 语言偏好
    - timezone: 时区设置
    - subscription_status: 订阅状态
    - total_readings: 总解读次数
    - last_reading_at: 最后解读时间
    - created_at/updated_at: 创建和更新时间
    - is_active: 账户是否激活
*/

-- 创建 users 表
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(100),
  avatar_url TEXT,
  credits INTEGER DEFAULT 1,
  language_preference VARCHAR(10) DEFAULT 'zh',
  timezone VARCHAR(50) DEFAULT 'UTC',
  subscription_status VARCHAR(20) DEFAULT 'free',
  total_readings INTEGER DEFAULT 0,
  last_reading_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE
);

-- 创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_users_clerk_user_id ON users(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_credits ON users(credits);
CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON users(subscription_status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at DESC);

-- 添加约束
ALTER TABLE users ADD CONSTRAINT chk_users_credits_non_negative 
  CHECK (credits >= 0);

ALTER TABLE users ADD CONSTRAINT chk_users_total_readings_non_negative 
  CHECK (total_readings >= 0);

ALTER TABLE users ADD CONSTRAINT chk_users_subscription_status 
  CHECK (subscription_status IN ('free', 'premium', 'vip'));

ALTER TABLE users ADD CONSTRAINT chk_users_language_preference 
  CHECK (language_preference IN ('zh', 'en'));

-- 启用 RLS (Row Level Security)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略：用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (
    auth.uid()::text = clerk_user_id OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (
    auth.uid()::text = clerk_user_id OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (
    auth.uid()::text = clerk_user_id OR
    auth.role() = 'service_role'
  );

-- 服务角色可以管理所有用户数据
CREATE POLICY "Service role can manage all users" ON users
  FOR ALL USING (auth.role() = 'service_role');

-- 开发环境特殊策略：允许 dev-user-123 访问
CREATE POLICY "Dev user can access all users" ON users
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

-- 创建更新时间触发器
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入测试用户数据（开发环境）
INSERT INTO users (
  clerk_user_id, 
  email, 
  name, 
  credits, 
  language_preference, 
  subscription_status,
  total_readings
) VALUES (
  'dev-user-123',
  '<EMAIL>',
  'Development User',
  100,
  'zh',
  'free',
  0
) ON CONFLICT (clerk_user_id) DO UPDATE SET
  credits = EXCLUDED.credits,
  updated_at = timezone('utc'::text, now());

-- 添加注释
COMMENT ON TABLE users IS '用户基础信息表 - 存储所有用户的核心数据';
COMMENT ON COLUMN users.clerk_user_id IS '与 Supabase Auth 关联的外部用户ID';
COMMENT ON COLUMN users.credits IS '用户积分余额 - 统一积分存储';
COMMENT ON COLUMN users.total_readings IS '用户总解读次数统计';
COMMENT ON COLUMN users.subscription_status IS '订阅状态: free, premium, vip';
COMMENT ON COLUMN users.language_preference IS '语言偏好: zh, en';
