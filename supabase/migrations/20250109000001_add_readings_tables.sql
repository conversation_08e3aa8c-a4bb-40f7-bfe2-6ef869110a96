/*
  # 添加八字解读相关表
  
  1. 新表
    - readings: 存储八字解读结果
    - credits: 用户积分表
    - credit_transactions: 积分交易记录表
    
  2. 安全性
    - 启用 RLS (Row Level Security)
    - 用户只能访问自己的数据
*/

-- 创建八字解读表
CREATE TABLE IF NOT EXISTS readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  birth_info JSONB NOT NULL,
  bazi_chart JSONB NOT NULL,
  analysis JSONB NOT NULL,
  crystal_recommendations JSONB NOT NULL,
  credits_consumed INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建用户积分表
CREATE TABLE IF NOT EXISTS credits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  balance INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建积分交易记录表
CREATE TABLE IF NOT EXISTS credit_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('purchase', 'consume', 'refund', 'bonus', 'admin_adjust', 'expire')),
  amount INTEGER NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_readings_user_id ON readings(user_id);
CREATE INDEX IF NOT EXISTS idx_readings_created_at ON readings(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_credits_user_id ON credits(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions(created_at DESC);

-- 启用 RLS
ALTER TABLE readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略：用户只能访问自己的数据
CREATE POLICY "Users can view own readings" ON readings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own readings" ON readings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own credits" ON credits
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own credits" ON credits
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credits" ON credits
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own credit transactions" ON credit_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own credit transactions" ON credit_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 服务角色可以管理所有数据
CREATE POLICY "Service role can manage readings" ON readings
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage credits" ON credits
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage credit transactions" ON credit_transactions
  FOR ALL USING (auth.role() = 'service_role');

-- 创建更新时间触发器
CREATE TRIGGER update_readings_updated_at 
  BEFORE UPDATE ON readings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_credits_updated_at 
  BEFORE UPDATE ON credits 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入测试用户积分数据（开发环境）
INSERT INTO credits (user_id, balance) VALUES
  ('dev-user-123'::uuid, 100)
ON CONFLICT (user_id) DO UPDATE SET
  balance = EXCLUDED.balance,
  updated_at = timezone('utc'::text, now());

-- 插入一些测试积分交易记录
INSERT INTO credit_transactions (user_id, type, amount, description) VALUES
  ('dev-user-123'::uuid, 'bonus', 100, 'Welcome bonus for new user'),
  ('dev-user-123'::uuid, 'consume', -10, 'BaZi reading analysis')
ON CONFLICT DO NOTHING;
