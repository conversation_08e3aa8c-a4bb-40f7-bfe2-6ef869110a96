/*
  # 修复外键关系 - 数据库代码一致性修复

  1. 问题
    - 原有表的外键引用 auth.users(id)，但代码使用自定义 users 表
    - RLS 策略中的用户ID映射逻辑混乱
    - credit_transactions 表的外键关系需要更新

  2. 解决方案
    - 确保所有表都正确引用 users(id)
    - 更新 RLS 策略使用正确的用户ID映射
    - 修复数据关联关系
    - 清理过时的策略和约束

  3. 修复内容
    - 更新 credit_transactions 外键关系
    - 修复所有 RLS 策略
    - 确保用户ID映射逻辑一致
    - 添加必要的索引
*/

-- 修复 credit_transactions 表的外键关系和结构
DO $$
BEGIN
  -- 检查 credit_transactions 表是否存在
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    
    -- 删除旧的外键约束（如果存在）
    ALTER TABLE credit_transactions DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey;
    
    -- 检查 user_id 字段类型是否正确
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'credit_transactions' 
      AND column_name = 'user_id' 
      AND data_type != 'uuid'
    ) THEN
      -- 如果 user_id 不是 UUID 类型，需要先清理数据然后修改类型
      RAISE NOTICE 'Converting credit_transactions.user_id to UUID type';
      
      -- 备份现有数据
      CREATE TABLE IF NOT EXISTS credit_transactions_backup_20250110 AS 
      SELECT * FROM credit_transactions;
      
      -- 清空表以便修改字段类型
      TRUNCATE credit_transactions;
      
      -- 修改字段类型
      ALTER TABLE credit_transactions ALTER COLUMN user_id TYPE UUID USING user_id::uuid;
    END IF;
    
    -- 添加新的外键约束，引用 users 表
    ALTER TABLE credit_transactions 
    ADD CONSTRAINT credit_transactions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    
    RAISE NOTICE 'Updated credit_transactions foreign key constraint';
    
  ELSE
    RAISE NOTICE 'credit_transactions table does not exist, skipping foreign key update';
  END IF;
END $$;

-- 清理和重建所有 RLS 策略
-- 删除所有旧的 RLS 策略
DROP POLICY IF EXISTS "Users can view own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Users can insert own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Service role can manage credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Dev user can access all credit transactions" ON credit_transactions;

-- 为 credit_transactions 创建新的 RLS 策略
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    
    -- 用户可以查看自己的积分交易记录
    CREATE POLICY "Users can view own credit transactions" ON credit_transactions
      FOR SELECT USING (
        user_id IN (
          SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
        ) OR
        auth.role() = 'service_role'
      );

    -- 用户可以插入自己的积分交易记录（通常由系统代为插入）
    CREATE POLICY "Users can insert own credit transactions" ON credit_transactions
      FOR INSERT WITH CHECK (
        user_id IN (
          SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
        ) OR
        auth.role() = 'service_role'
      );

    -- 服务角色可以管理所有积分交易记录
    CREATE POLICY "Service role can manage all credit transactions" ON credit_transactions
      FOR ALL USING (auth.role() = 'service_role');

    -- 开发环境特殊策略
    CREATE POLICY "Dev user can access all credit transactions" ON credit_transactions
      FOR ALL USING (
        auth.uid()::text = 'dev-user-123' OR
        auth.role() = 'service_role'
      );
    
    RAISE NOTICE 'Created RLS policies for credit_transactions';
  END IF;
END $$;

-- 确保所有表的 RLS 策略都使用一致的用户ID映射逻辑
-- 更新 readings 表的 RLS 策略（如果需要）
DROP POLICY IF EXISTS "Users can view own readings" ON readings;
DROP POLICY IF EXISTS "Users can insert own readings" ON readings;
DROP POLICY IF EXISTS "Users can update own readings" ON readings;
DROP POLICY IF EXISTS "Service role can manage all readings" ON readings;
DROP POLICY IF EXISTS "Dev user can access all readings" ON readings;

-- 重新创建 readings 表的 RLS 策略
CREATE POLICY "Users can view own readings" ON readings
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own readings" ON readings
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can update own readings" ON readings
  FOR UPDATE USING (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Service role can manage all readings" ON readings
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Dev user can access all readings" ON readings
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

-- 创建一个辅助函数来获取当前用户的数据库ID
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT id FROM users 
    WHERE clerk_user_id = auth.uid()::text
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建一个函数来验证用户是否有权限访问特定记录
CREATE OR REPLACE FUNCTION user_can_access_record(record_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    record_user_id = get_current_user_id() OR
    auth.role() = 'service_role' OR
    auth.uid()::text = 'dev-user-123'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加必要的索引来提升 RLS 策略的性能
CREATE INDEX IF NOT EXISTS idx_users_clerk_user_id_lookup ON users(clerk_user_id) 
WHERE clerk_user_id IS NOT NULL;

-- 为 credit_transactions 添加复合索引
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_created ON credit_transactions(user_id, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_credit_transactions_type_status ON credit_transactions(type, status);
  END IF;
END $$;

-- 为 readings 添加复合索引
CREATE INDEX IF NOT EXISTS idx_readings_user_created ON readings(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_readings_user_type ON readings(user_id, type);

-- 验证外键关系
DO $$
DECLARE
  fk_count INTEGER;
BEGIN
  -- 检查 readings 表的外键
  SELECT COUNT(*) INTO fk_count
  FROM information_schema.table_constraints tc
  JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
  WHERE tc.table_name = 'readings' 
  AND tc.constraint_type = 'FOREIGN KEY'
  AND kcu.column_name = 'user_id'
  AND kcu.referenced_table_name = 'users';
  
  IF fk_count > 0 THEN
    RAISE NOTICE 'readings table foreign key to users is correctly configured';
  ELSE
    RAISE WARNING 'readings table foreign key to users is missing';
  END IF;
  
  -- 检查 credit_transactions 表的外键（如果表存在）
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    SELECT COUNT(*) INTO fk_count
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_name = 'credit_transactions' 
    AND tc.constraint_type = 'FOREIGN KEY'
    AND kcu.column_name = 'user_id'
    AND kcu.referenced_table_name = 'users';
    
    IF fk_count > 0 THEN
      RAISE NOTICE 'credit_transactions table foreign key to users is correctly configured';
    ELSE
      RAISE WARNING 'credit_transactions table foreign key to users is missing';
    END IF;
  END IF;
END $$;

-- 添加注释
COMMENT ON FUNCTION get_current_user_id() IS '获取当前认证用户在 users 表中的 ID';
COMMENT ON FUNCTION user_can_access_record(UUID) IS '检查当前用户是否有权限访问指定用户ID的记录';
