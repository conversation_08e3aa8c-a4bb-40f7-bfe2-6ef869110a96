/*
  # 创建 credit_packages 表 - 数据库代码一致性修复

  1. 问题
    - 代码中查询 credit_packages 表，但只有数据插入，没有表结构定义
    - 积分套餐购买功能无法正常工作
    - 缺少表结构导致 API 调用失败

  2. 解决方案
    - 根据代码中的实际使用和现有数据插入语句创建表结构
    - 包含所有必要的字段和约束
    - 设置合适的索引和默认值
    - 支持多语言和多货币

  3. 字段说明
    - id: 主键，UUID 类型
    - name/name_zh: 套餐名称（英文/中文）
    - description: 套餐描述
    - credits: 包含的积分数量
    - price_usd/price_cny: 美元/人民币价格
    - currency: 主要货币
    - bonus_credits: 赠送积分
    - validity_days: 有效期天数
    - package_type: 套餐类型（standard, premium, promotional）
    - display_order: 显示顺序
    - is_popular/is_featured: 标记字段
    - is_active: 是否启用
*/

-- 创建 credit_packages 表
CREATE TABLE IF NOT EXISTS credit_packages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  name_zh VARCHAR(100),
  description TEXT,
  credits INTEGER NOT NULL,
  price_usd DECIMAL(10,2),
  price_cny DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  bonus_credits INTEGER DEFAULT 0,
  validity_days INTEGER DEFAULT 365,
  package_type VARCHAR(20) DEFAULT 'standard',
  display_order INTEGER DEFAULT 0,
  is_popular BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_credit_packages_is_active ON credit_packages(is_active);
CREATE INDEX IF NOT EXISTS idx_credit_packages_package_type ON credit_packages(package_type);
CREATE INDEX IF NOT EXISTS idx_credit_packages_display_order ON credit_packages(display_order);
CREATE INDEX IF NOT EXISTS idx_credit_packages_is_popular ON credit_packages(is_popular);
CREATE INDEX IF NOT EXISTS idx_credit_packages_is_featured ON credit_packages(is_featured);
CREATE INDEX IF NOT EXISTS idx_credit_packages_credits ON credit_packages(credits);

-- 添加约束
ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_credits_positive 
  CHECK (credits > 0);

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_bonus_credits_non_negative 
  CHECK (bonus_credits >= 0);

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_validity_days_positive 
  CHECK (validity_days > 0);

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_price_usd_non_negative 
  CHECK (price_usd >= 0);

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_price_cny_non_negative 
  CHECK (price_cny >= 0);

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_package_type 
  CHECK (package_type IN ('standard', 'premium', 'promotional'));

ALTER TABLE credit_packages ADD CONSTRAINT chk_credit_packages_currency 
  CHECK (currency IN ('USD', 'CNY', 'EUR', 'GBP'));

-- 启用 RLS (Row Level Security)
ALTER TABLE credit_packages ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略：所有人可以查看活跃的套餐，只有服务角色可以修改
CREATE POLICY "Anyone can view active credit packages" ON credit_packages
  FOR SELECT USING (is_active = TRUE);

CREATE POLICY "Service role can manage credit packages" ON credit_packages
  FOR ALL USING (auth.role() = 'service_role');

-- 创建更新时间触发器
CREATE TRIGGER update_credit_packages_updated_at 
  BEFORE UPDATE ON credit_packages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始套餐数据（基于现有的数据插入语句）
INSERT INTO credit_packages (
  name,
  name_zh,
  description,
  credits,
  price_usd,
  price_cny,
  currency,
  bonus_credits,
  validity_days,
  package_type,
  display_order,
  is_popular,
  is_featured,
  is_active
) VALUES
  (
    'Starter Pack',
    '新手套餐',
    '适合初次体验的用户',
    50,
    9.99,
    68.00,
    'USD',
    5,
    90,
    'standard',
    1,
    false,
    false,
    true
  ),
  (
    'Popular Pack',
    '热门套餐',
    '最受欢迎的积分套餐',
    120,
    19.99,
    138.00,
    'USD',
    20,
    180,
    'standard',
    2,
    true,
    false,
    true
  ),
  (
    'Premium Pack',
    '高级套餐',
    '高级用户的最佳选择',
    300,
    39.99,
    268.00,
    'USD',
    60,
    365,
    'premium',
    3,
    false,
    true,
    true
  ),
  (
    'Ultimate Pack',
    '终极套餐',
    '终极积分套餐，享受最大优惠',
    600,
    69.99,
    468.00,
    'USD',
    150,
    365,
    'premium',
    4,
    false,
    false,
    true
  ),
  (
    'New Year Special',
    '新年特惠',
    '新年特惠套餐，限时优惠',
    200,
    24.99,
    168.00,
    'USD',
    80,
    180,
    'promotional',
    5,
    false,
    true,
    true
  )
ON CONFLICT DO NOTHING;

-- 添加注释
COMMENT ON TABLE credit_packages IS '积分套餐表 - 存储可购买的积分套餐信息';
COMMENT ON COLUMN credit_packages.name IS '套餐名称（英文）';
COMMENT ON COLUMN credit_packages.name_zh IS '套餐名称（中文）';
COMMENT ON COLUMN credit_packages.credits IS '包含的积分数量';
COMMENT ON COLUMN credit_packages.bonus_credits IS '赠送的额外积分';
COMMENT ON COLUMN credit_packages.package_type IS '套餐类型: standard, premium, promotional';
COMMENT ON COLUMN credit_packages.display_order IS '显示顺序，数字越小越靠前';
COMMENT ON COLUMN credit_packages.is_popular IS '是否为热门套餐';
COMMENT ON COLUMN credit_packages.is_featured IS '是否为推荐套餐';
