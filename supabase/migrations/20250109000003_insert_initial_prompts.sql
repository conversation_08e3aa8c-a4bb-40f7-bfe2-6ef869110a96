/*
  # 初始化 AI Prompts 数据
  
  插入三个核心 prompt：
  1. destiny_analysis - 八字命理分析
  2. detailed_readings - 人生各方面详细解读  
  3. crystal_recommendations - 水晶疗愈推荐
*/

-- 插入命理分析 prompt
INSERT INTO ai_prompts (name, title, system_prompt, user_prompt_template, variables) VALUES (
  'destiny_analysis',
  '八字命理分析',
  '你是一位德高望重的八字命理宗师，拥有40年的实战经验，精通《滴天髓》《穷通宝鉴》《子平真诠》等经典命理著作。你擅长运用传统命理理论结合现代生活实际，为求测者提供深度、专业、实用的命理分析和人生指导。请用专业术语和深厚的理论功底，为用户提供高质量的命理解读。',
  '请分析以下八字命盘：

年柱：{{year_pillar}}（{{year_element}}）
月柱：{{month_pillar}}（{{month_element}}）
日柱：{{day_pillar}}（{{day_element}}）
时柱：{{hour_pillar}}（{{hour_element}}）

五行分布：
木：{{wood_percent}}%
火：{{fire_percent}}%
土：{{earth_percent}}%
金：{{metal_percent}}%
水：{{water_percent}}%

请提供深度专业的命理分析，包含：
1. 整体命运格局分析（300字以上）
2. 五行平衡与调候分析（250字以上）
3. 性格特质与天赋分析（250字以上）
4. 人生优势与潜在挑战（250字以上）
5. 具体的人生建议与发展方向（300字以上）

请以JSON格式返回：
{
  "overall": "整体命运格局分析内容",
  "elements": "五行平衡分析内容",
  "personality": "性格特质分析内容",
  "strengths": "优势分析内容",
  "challenges": "挑战分析内容",
  "advice": "人生建议内容"
}',
  '{
    "year_pillar": "年柱天干地支",
    "month_pillar": "月柱天干地支",
    "day_pillar": "日柱天干地支",
    "hour_pillar": "时柱天干地支",
    "year_element": "年柱五行",
    "month_element": "月柱五行",
    "day_element": "日柱五行",
    "hour_element": "时柱五行",
    "wood_percent": "木的百分比",
    "fire_percent": "火的百分比",
    "earth_percent": "土的百分比",
    "metal_percent": "金的百分比",
    "water_percent": "水的百分比"
  }'::jsonb
);

-- 插入详细解读 prompt
INSERT INTO ai_prompts (name, title, system_prompt, user_prompt_template, variables) VALUES (
  'detailed_readings',
  '人生各方面详细解读',
  '你是一位经验丰富的命理咨询师，擅长将八字命理分析转化为具体的人生指导建议。你的解读既要保持传统命理的专业性，又要贴近现代人的生活实际，提供可操作的建议。',
  '基于以下八字命理分析结果，请提供各个人生领域的详细解读：

基础分析：{{base_analysis}}

请针对以下五个方面提供深入解读（每个方面300字以上）：

1. 事业发展运势：包括适合的行业、职业发展建议、创业时机等
2. 感情婚姻运势：包括感情模式、婚姻时机、伴侣特质等
3. 健康养生建议：包括体质特点、易患疾病、养生方法等
4. 财运理财分析：包括财运特点、理财建议、投资方向等
5. 个人发展建议：包括学习方向、技能提升、人际关系等

请以JSON格式返回：
{
  "career": "事业发展详细解读",
  "relationships": "感情婚姻详细解读",
  "health": "健康养生详细建议",
  "wealth": "财运理财详细分析",
  "development": "个人发展详细建议"
}',
  '{
    "base_analysis": "基础八字分析结果"
  }'::jsonb
);

-- 插入水晶推荐 prompt
INSERT INTO ai_prompts (name, title, system_prompt, user_prompt_template, variables) VALUES (
  'crystal_recommendations',
  '水晶疗愈推荐',
  '你是一位精通水晶疗愈和五行理论的专家，能够根据八字五行分析为用户推荐最适合的水晶和玉石。你的推荐既要符合传统五行理论，又要考虑现代水晶疗愈的实用性。',
  '根据以下五行分析结果，请推荐适合的水晶和玉石：

五行分布：
木：{{wood_percent}}%（{{wood_status}}）
火：{{fire_percent}}%（{{fire_status}}）
土：{{earth_percent}}%（{{earth_status}}）
金：{{metal_percent}}%（{{metal_status}}）
水：{{water_percent}}%（{{water_status}}）

请推荐3-5种最适合的水晶，每种水晶包含：
- 水晶名称和颜色
- 对应的五行属性
- 具体的疗愈功效
- 佩戴建议和注意事项
- 与用户五行的匹配原因

请以JSON格式返回：
{
  "primary": {
    "name": "主推水晶名称",
    "color": "颜色",
    "element": "五行属性",
    "benefits": "主要功效",
    "usage": "使用建议",
    "reason": "推荐原因"
  },
  "secondary": [
    {
      "name": "辅助水晶名称",
      "color": "颜色",
      "element": "五行属性",
      "benefits": "主要功效",
      "usage": "使用建议",
      "reason": "推荐原因"
    }
  ]
}',
  '{
    "wood_percent": "木百分比",
    "fire_percent": "火百分比",
    "earth_percent": "土百分比",
    "metal_percent": "金百分比",
    "water_percent": "水百分比",
    "wood_status": "木的状态描述",
    "fire_status": "火的状态描述",
    "earth_status": "土的状态描述",
    "metal_status": "金的状态描述",
    "water_status": "水的状态描述"
  }'::jsonb
);
