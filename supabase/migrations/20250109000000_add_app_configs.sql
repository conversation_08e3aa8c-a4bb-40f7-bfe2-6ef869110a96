/*
  # 添加应用配置表
  
  1. 新表
    - app_configs: 存储所有应用配置，包括 API 密钥等敏感信息
    
  2. 安全性
    - 启用 RLS (Row Level Security)
    - 只有服务角色可以访问敏感配置
    - 普通用户无法访问任何配置
*/

-- 创建应用配置表
CREATE TABLE IF NOT EXISTS app_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key VARCHAR(255) NOT NULL UNIQUE,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_app_configs_key ON app_configs(key);
CREATE INDEX IF NOT EXISTS idx_app_configs_is_secret ON app_configs(is_secret);

-- 启用 RLS
ALTER TABLE app_configs ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略：只有服务角色可以访问
CREATE POLICY "Service role can manage app configs" ON app_configs
  FOR ALL USING (auth.role() = 'service_role');

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_app_configs_updated_at 
  BEFORE UPDATE ON app_configs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始配置数据
INSERT INTO app_configs (key, value, description, is_secret) VALUES
  ('DEEPSEEK_API_KEY', '***********************************', 'DeepSeek API 密钥', true),
  ('DEEPSEEK_BASE_URL', 'https://api.deepseek.com', 'DeepSeek API 基础URL', false),
  ('DEEPSEEK_MAX_TOKENS', '2000', 'DeepSeek API 最大令牌数', false),
  ('DEEPSEEK_TEMPERATURE', '0.7', 'DeepSeek API 温度参数', false),
  ('SUPABASE_URL', 'https://tagcspajmlnnnwdrwtvf.supabase.co', 'Supabase 项目URL', false),
  ('SUPABASE_SERVICE_KEY', 'your_service_key_here', 'Supabase 服务密钥', true),
  ('API_RATE_LIMIT_PER_MINUTE', '60', 'API 每分钟请求限制', false),
  ('API_RATE_LIMIT_PER_HOUR', '1000', 'API 每小时请求限制', false),
  ('CACHE_TTL_SECONDS', '300', '缓存过期时间（秒）', false),
  ('READING_COST_CREDITS', '10', '创建解读消耗的积分', false),
  ('PREMIUM_CONTENT_COST_CREDITS', '5', '解锁高级内容消耗的积分', false)
ON CONFLICT (key) DO UPDATE SET
  value = EXCLUDED.value,
  description = EXCLUDED.description,
  is_secret = EXCLUDED.is_secret,
  updated_at = timezone('utc'::text, now());
