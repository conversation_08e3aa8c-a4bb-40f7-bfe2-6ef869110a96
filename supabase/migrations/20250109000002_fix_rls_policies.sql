/*
  # 修复 RLS 策略以支持开发环境
  
  1. 问题
    - 当前 RLS 策略要求 auth.uid() = user_id
    - 但在开发环境中，我们使用自定义的用户 ID 映射
    - 前端使用的是 users 表的 id，而不是 auth.users 的 id
    
  2. 解决方案
    - 修改 RLS 策略，支持通过 clerk_user_id 进行匹配
    - 在开发环境中允许服务角色进行所有操作
    - 添加更灵活的用户匹配逻辑
*/

-- 删除现有的 RLS 策略
DROP POLICY IF EXISTS "Users can view own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Users can insert own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Users can view own credits" ON credits;
DROP POLICY IF EXISTS "Users can insert own credits" ON credits;
DROP POLICY IF EXISTS "Users can update own credits" ON credits;

-- 创建新的 RLS 策略，支持开发环境的用户 ID 映射
CREATE POLICY "Users can view own credit transactions" ON credit_transactions
  FOR SELECT USING (
    auth.uid()::text = user_id::text OR 
    auth.uid()::text IN (
      SELECT clerk_user_id FROM users WHERE id = credit_transactions.user_id
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own credit transactions" ON credit_transactions
  FOR INSERT WITH CHECK (
    auth.uid()::text = user_id::text OR 
    auth.uid()::text IN (
      SELECT clerk_user_id FROM users WHERE id = credit_transactions.user_id
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can view own credits" ON credits
  FOR SELECT USING (
    auth.uid()::text = user_id::text OR 
    auth.uid()::text IN (
      SELECT clerk_user_id FROM users WHERE id = credits.user_id
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own credits" ON credits
  FOR INSERT WITH CHECK (
    auth.uid()::text = user_id::text OR 
    auth.uid()::text IN (
      SELECT clerk_user_id FROM users WHERE id = credits.user_id
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can update own credits" ON credits
  FOR UPDATE USING (
    auth.uid()::text = user_id::text OR 
    auth.uid()::text IN (
      SELECT clerk_user_id FROM users WHERE id = credits.user_id
    ) OR
    auth.role() = 'service_role'
  );

-- 为开发环境添加特殊策略：允许 dev-user-123 访问所有数据
CREATE POLICY "Dev user can access all credit transactions" ON credit_transactions
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Dev user can access all credits" ON credits
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

-- 确保测试用户的积分交易记录使用正确的 user_id
UPDATE credit_transactions 
SET user_id = (SELECT id FROM users WHERE clerk_user_id = 'dev-user-123')
WHERE user_id::text = 'dev-user-123';

-- 确保测试用户的积分记录使用正确的 user_id
UPDATE credits 
SET user_id = (SELECT id FROM users WHERE clerk_user_id = 'dev-user-123')
WHERE user_id::text = 'dev-user-123';
