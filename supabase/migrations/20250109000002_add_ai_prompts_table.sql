/*
  # 添加 AI Prompts 表
  
  1. 新表
    - ai_prompts: 存储所有 AI 提示词模板
    
  2. 安全性
    - 启用 RLS (Row Level Security)
    - 所有人可读，只有服务角色可写
*/

-- 创建 ai_prompts 表
CREATE TABLE IF NOT EXISTS ai_prompts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,           -- prompt标识名称
  title VARCHAR(200) NOT NULL,                 -- 显示标题
  system_prompt TEXT NOT NULL,                 -- 系统提示词
  user_prompt_template TEXT NOT NULL,          -- 用户提示词模板
  variables JSONB DEFAULT '{}'::jsonb,         -- 模板变量定义
  is_active BOOLEAN DEFAULT true,              -- 是否启用
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_prompts_name ON ai_prompts(name);
CREATE INDEX IF NOT EXISTS idx_ai_prompts_active ON ai_prompts(is_active);

-- 启用 RLS
ALTER TABLE ai_prompts ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略（简化版）
CREATE POLICY "ai_prompts_read_policy" ON ai_prompts
  FOR SELECT USING (true); -- 所有人可读

CREATE POLICY "ai_prompts_write_policy" ON ai_prompts
  FOR ALL USING (auth.role() = 'service_role'); -- 只有服务角色可写

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_ai_prompts_updated_at 
    BEFORE UPDATE ON ai_prompts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
