/*
  # Create initial credit packages

  1. New Data
    - Insert default credit packages for the application
    - Standard, premium, and promotional packages
    - Different price points and bonus structures

  2. Security
    - No additional RLS needed as packages are public data
*/

INSERT INTO credit_packages (
  name,
  description,
  credits,
  price_usd,
  price_cny,
  currency,
  bonus_credits,
  validity_days,
  package_type,
  display_order,
  is_popular,
  is_featured,
  is_active
) VALUES
  (
    'Starter Pack',
    '适合初次体验的用户',
    50,
    9.99,
    68.00,
    'USD',
    5,
    90,
    'standard',
    1,
    false,
    false,
    true
  ),
  (
    'Popular Pack',
    '最受欢迎的积分套餐',
    120,
    19.99,
    138.00,
    'USD',
    20,
    180,
    'standard',
    2,
    true,
    false,
    true
  ),
  (
    'Premium Pack',
    '高级用户的最佳选择',
    300,
    39.99,
    268.00,
    'USD',
    60,
    365,
    'premium',
    3,
    false,
    true,
    true
  ),
  (
    'Ultimate Pack',
    '终极积分套餐，享受最大优惠',
    600,
    69.99,
    468.00,
    'USD',
    150,
    365,
    'premium',
    4,
    false,
    false,
    true
  ),
  (
    'New Year Special',
    '新年特惠套餐，限时优惠',
    200,
    24.99,
    168.00,
    'USD',
    80,
    180,
    'promotional',
    5,
    false,
    true,
    true
  );