/*
  # 统一积分系统设计 - 数据库代码一致性修复

  1. 问题
    - 当前同时存在 users.credits 字段和独立的 credits 表
    - 造成双重积分存储的混乱设计
    - 代码中主要使用 users.credits，但数据库有独立的 credits 表
    - RLS 策略引用了不存在的 users 表

  2. 解决方案
    - 迁移 credits 表中的数据到 users.credits 字段
    - 删除独立的 credits 表
    - 更新相关的 RLS 策略
    - 确保积分系统统一使用 users.credits

  3. 迁移步骤
    - 备份 credits 表数据
    - 将积分数据合并到 users 表
    - 删除 credits 表及相关对象
    - 清理相关的 RLS 策略
*/

-- 备份现有 credits 表数据（如果存在）
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credits') THEN
    -- 检查是否有数据需要备份
    IF (SELECT COUNT(*) FROM credits) > 0 THEN
      -- 创建备份表
      CREATE TABLE credits_backup_20250110 AS SELECT * FROM credits;
      RAISE NOTICE 'Backed up % rows to credits_backup_20250110', (SELECT COUNT(*) FROM credits_backup_20250110);
      
      -- 尝试迁移积分数据到 users 表
      -- 注意：这里需要处理 user_id 的映射关系
      UPDATE users 
      SET credits = COALESCE(c.balance, users.credits),
          updated_at = timezone('utc'::text, now())
      FROM credits c 
      WHERE users.id = c.user_id;
      
      RAISE NOTICE 'Migrated credit balances to users table';
    END IF;
  END IF;
END $$;

-- 删除与 credits 表相关的 RLS 策略
DROP POLICY IF EXISTS "Users can view own credits" ON credits;
DROP POLICY IF EXISTS "Users can update own credits" ON credits;
DROP POLICY IF EXISTS "Users can insert own credits" ON credits;
DROP POLICY IF EXISTS "Service role can manage credits" ON credits;
DROP POLICY IF EXISTS "Dev user can access all credits" ON credits;

-- 删除 credits 表的触发器
DROP TRIGGER IF EXISTS update_credits_updated_at ON credits;

-- 删除 credits 表及其索引
DROP TABLE IF EXISTS credits CASCADE;

-- 更新 credit_transactions 表，确保它正确引用 users 表
-- 首先检查 credit_transactions 表是否存在且结构正确
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    -- 检查外键约束是否正确
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'credit_transactions' 
      AND tc.constraint_type = 'FOREIGN KEY'
      AND kcu.column_name = 'user_id'
      AND kcu.referenced_table_name = 'users'
    ) THEN
      -- 删除旧的外键约束（如果存在）
      ALTER TABLE credit_transactions DROP CONSTRAINT IF EXISTS credit_transactions_user_id_fkey;
      
      -- 添加新的外键约束，引用 users 表
      ALTER TABLE credit_transactions 
      ADD CONSTRAINT credit_transactions_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
      
      RAISE NOTICE 'Updated credit_transactions foreign key to reference users table';
    END IF;
  END IF;
END $$;

-- 更新 credit_transactions 的 RLS 策略
DROP POLICY IF EXISTS "Users can view own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Users can insert own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Dev user can access all credit transactions" ON credit_transactions;

-- 重新创建 credit_transactions 的 RLS 策略
CREATE POLICY "Users can view own credit transactions" ON credit_transactions
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own credit transactions" ON credit_transactions
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Service role can manage all credit transactions" ON credit_transactions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Dev user can access all credit transactions" ON credit_transactions
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

-- 确保 credit_transactions 表有正确的字段结构
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    -- 检查是否缺少必要的字段
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'credit_transactions' AND column_name = 'balance_before'
    ) THEN
      ALTER TABLE credit_transactions ADD COLUMN balance_before INTEGER;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'credit_transactions' AND column_name = 'balance_after'
    ) THEN
      ALTER TABLE credit_transactions ADD COLUMN balance_after INTEGER;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'credit_transactions' AND column_name = 'reference_id'
    ) THEN
      ALTER TABLE credit_transactions ADD COLUMN reference_id VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'credit_transactions' AND column_name = 'status'
    ) THEN
      ALTER TABLE credit_transactions ADD COLUMN status VARCHAR(20) DEFAULT 'completed';
    END IF;
    
    RAISE NOTICE 'Updated credit_transactions table structure';
  END IF;
END $$;

-- 添加 credit_transactions 表的约束（如果不存在）
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    -- 添加状态约束
    BEGIN
      ALTER TABLE credit_transactions ADD CONSTRAINT chk_credit_transactions_status 
        CHECK (status IN ('pending', 'completed', 'failed', 'cancelled'));
    EXCEPTION
      WHEN duplicate_object THEN
        NULL; -- 约束已存在，忽略
    END;
    
    -- 添加余额约束
    BEGIN
      ALTER TABLE credit_transactions ADD CONSTRAINT chk_credit_transactions_balance_before_non_negative 
        CHECK (balance_before >= 0);
    EXCEPTION
      WHEN duplicate_object THEN
        NULL; -- 约束已存在，忽略
    END;
    
    BEGIN
      ALTER TABLE credit_transactions ADD CONSTRAINT chk_credit_transactions_balance_after_non_negative 
        CHECK (balance_after >= 0);
    EXCEPTION
      WHEN duplicate_object THEN
        NULL; -- 约束已存在，忽略
    END;
  END IF;
END $$;

-- 创建一个函数来自动更新用户的 total_readings 计数
CREATE OR REPLACE FUNCTION update_user_total_readings()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.type = 'bazi' AND NEW.status = 'completed' THEN
    UPDATE users 
    SET total_readings = total_readings + 1,
        last_reading_at = NEW.created_at,
        updated_at = timezone('utc'::text, now())
    WHERE id = NEW.user_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器来自动更新用户统计
DROP TRIGGER IF EXISTS trigger_update_user_total_readings ON readings;
CREATE TRIGGER trigger_update_user_total_readings
  AFTER INSERT ON readings
  FOR EACH ROW
  EXECUTE FUNCTION update_user_total_readings();

-- 添加注释
COMMENT ON FUNCTION update_user_total_readings() IS '自动更新用户的解读次数统计和最后解读时间';

-- 验证数据一致性
DO $$
DECLARE
  credits_table_exists BOOLEAN;
  users_table_exists BOOLEAN;
BEGIN
  SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credits') INTO credits_table_exists;
  SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users') INTO users_table_exists;
  
  IF credits_table_exists THEN
    RAISE WARNING 'Credits table still exists - migration may have failed';
  ELSE
    RAISE NOTICE 'Credits table successfully removed';
  END IF;
  
  IF users_table_exists THEN
    RAISE NOTICE 'Users table exists with credits field - unified credit system ready';
  ELSE
    RAISE WARNING 'Users table does not exist - this should not happen';
  END IF;
END $$;
