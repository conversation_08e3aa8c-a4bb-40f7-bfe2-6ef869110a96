/*
  # 重构 readings 表结构 - 数据库代码一致性修复

  1. 问题
    - 现有 readings 表字段与代码期望完全不匹配
    - 数据库字段: birth_info, bazi_chart, analysis, crystal_recommendations (JSONB)
    - 代码期望字段: type, status, birth_date, birth_time, birth_location, timezone, gender, result, summary, language
    - 导致八字分析结果无法正确保存

  2. 解决方案
    - 备份现有数据（如果有）
    - 删除现有 readings 表
    - 根据代码实际使用重新创建表结构
    - 更新外键关系指向新的 users 表
    - 重新设置 RLS 策略

  3. 新表结构说明
    - id: 主键，UUID 类型
    - user_id: 外键，引用 users(id)
    - type: 解读类型（如 'bazi'）
    - status: 解读状态（如 'completed'）
    - birth_date: 出生日期
    - birth_time: 出生时间
    - birth_location: 出生地点（JSONB）
    - timezone: 时区
    - gender: 性别
    - result: 分析结果（JSONB，包含所有分析数据）
    - summary: 分析摘要
    - language: 语言
    - credits_consumed: 消费的积分
*/

-- 备份现有 readings 表数据（如果存在且有数据）
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'readings') THEN
    -- 检查是否有数据需要备份
    IF (SELECT COUNT(*) FROM readings) > 0 THEN
      -- 创建备份表
      CREATE TABLE readings_backup_20250110 AS SELECT * FROM readings;
      RAISE NOTICE 'Backed up % rows to readings_backup_20250110', (SELECT COUNT(*) FROM readings_backup_20250110);
    END IF;
  END IF;
END $$;

-- 删除现有 readings 表及其相关对象
DROP TABLE IF EXISTS readings CASCADE;

-- 重新创建 readings 表，匹配代码期望
CREATE TABLE readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL DEFAULT 'bazi',
  status VARCHAR(20) DEFAULT 'completed',
  birth_date DATE NOT NULL,
  birth_time TIME,
  birth_location JSONB,
  timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
  gender VARCHAR(10),
  result JSONB NOT NULL,
  summary TEXT,
  language VARCHAR(10) DEFAULT 'zh',
  credits_consumed INTEGER DEFAULT 10,
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_readings_user_id ON readings(user_id);
CREATE INDEX IF NOT EXISTS idx_readings_created_at ON readings(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_readings_type ON readings(type);
CREATE INDEX IF NOT EXISTS idx_readings_status ON readings(status);
CREATE INDEX IF NOT EXISTS idx_readings_birth_date ON readings(birth_date);
CREATE INDEX IF NOT EXISTS idx_readings_language ON readings(language);

-- 添加约束
ALTER TABLE readings ADD CONSTRAINT chk_readings_credits_consumed_non_negative 
  CHECK (credits_consumed >= 0);

ALTER TABLE readings ADD CONSTRAINT chk_readings_type 
  CHECK (type IN ('bazi', 'tarot', 'numerology', 'astrology'));

ALTER TABLE readings ADD CONSTRAINT chk_readings_status 
  CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'));

ALTER TABLE readings ADD CONSTRAINT chk_readings_gender 
  CHECK (gender IN ('male', 'female', 'other'));

ALTER TABLE readings ADD CONSTRAINT chk_readings_language 
  CHECK (language IN ('zh', 'en'));

-- 启用 RLS (Row Level Security)
ALTER TABLE readings ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略：用户只能访问自己的解读记录
CREATE POLICY "Users can view own readings" ON readings
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can insert own readings" ON readings
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

CREATE POLICY "Users can update own readings" ON readings
  FOR UPDATE USING (
    user_id IN (
      SELECT id FROM users WHERE clerk_user_id = auth.uid()::text
    ) OR
    auth.role() = 'service_role'
  );

-- 服务角色可以管理所有解读记录
CREATE POLICY "Service role can manage all readings" ON readings
  FOR ALL USING (auth.role() = 'service_role');

-- 开发环境特殊策略：允许 dev-user-123 访问
CREATE POLICY "Dev user can access all readings" ON readings
  FOR ALL USING (
    auth.uid()::text = 'dev-user-123' OR
    auth.role() = 'service_role'
  );

-- 创建更新时间触发器
CREATE TRIGGER update_readings_updated_at 
  BEFORE UPDATE ON readings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入测试数据（开发环境）
DO $$
DECLARE
  test_user_id UUID;
BEGIN
  -- 获取测试用户的 ID
  SELECT id INTO test_user_id FROM users WHERE clerk_user_id = 'dev-user-123';
  
  IF test_user_id IS NOT NULL THEN
    INSERT INTO readings (
      user_id,
      type,
      status,
      birth_date,
      birth_time,
      birth_location,
      timezone,
      gender,
      result,
      summary,
      language,
      credits_consumed
    ) VALUES (
      test_user_id,
      'bazi',
      'completed',
      '1990-05-15',
      '14:30:00',
      '{"location": "北京市", "country": "China", "city": "Beijing"}'::jsonb,
      'Asia/Shanghai',
      'male',
      '{
        "bazi_chart": {
          "year": {"heavenly": "庚", "earthly": "午", "element": "金"},
          "month": {"heavenly": "辛", "earthly": "巳", "element": "金"},
          "day": {"heavenly": "甲", "earthly": "子", "element": "木"},
          "hour": {"heavenly": "辛", "earthly": "未", "element": "金"}
        },
        "element_profile": {
          "wood": 20,
          "fire": 25,
          "earth": 15,
          "metal": 30,
          "water": 10
        },
        "destiny_analysis": {
          "overall": "命主金旺木弱，需要调和五行平衡...",
          "personality": "性格坚毅，有领导才能...",
          "strengths": "意志坚定，执行力强...",
          "challenges": "容易固执，需要学会变通...",
          "advice": "建议多接触木属性事物..."
        }
      }'::jsonb,
      '八字分析完成 - 金旺木弱格局',
      'zh',
      10
    ) ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Inserted test reading for user %', test_user_id;
  ELSE
    RAISE NOTICE 'Test user not found, skipping test data insertion';
  END IF;
END $$;

-- 添加注释
COMMENT ON TABLE readings IS '八字解读记录表 - 存储用户的解读结果';
COMMENT ON COLUMN readings.user_id IS '用户ID，外键引用 users(id)';
COMMENT ON COLUMN readings.type IS '解读类型: bazi, tarot, numerology, astrology';
COMMENT ON COLUMN readings.status IS '解读状态: pending, processing, completed, failed, cancelled';
COMMENT ON COLUMN readings.birth_location IS '出生地点信息（JSON格式）';
COMMENT ON COLUMN readings.result IS '完整的分析结果（JSON格式，包含八字图表、五行分析、命理解读等）';
COMMENT ON COLUMN readings.summary IS '分析结果摘要';
COMMENT ON COLUMN readings.credits_consumed IS '本次解读消费的积分数量';
