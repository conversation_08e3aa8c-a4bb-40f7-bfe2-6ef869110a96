/*
  # 清理和优化数据库 - 数据库代码一致性修复

  1. 清理内容
    - 删除不使用的备份表
    - 清理过时的 RLS 策略
    - 删除无用的索引
    - 清理临时对象

  2. 优化内容
    - 添加性能优化索引
    - 优化查询计划
    - 添加统计信息
    - 设置合适的表参数

  3. 验证内容
    - 检查数据一致性
    - 验证外键关系
    - 确认 RLS 策略正确
    - 测试查询性能
*/

-- ============================================================================
-- 第一部分：清理冗余对象
-- ============================================================================

-- 清理可能存在的备份表（如果不再需要）
DO $$
BEGIN
  -- 检查是否有备份表需要清理（保留最近的备份）
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name LIKE '%_backup_%') THEN
    RAISE NOTICE 'Found backup tables. Review manually before dropping.';
    -- 不自动删除备份表，需要手动确认
  END IF;
END $$;

-- 清理可能存在的过时 RLS 策略
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  -- 查找可能的孤立策略
  FOR policy_record IN 
    SELECT schemaname, tablename, policyname 
    FROM pg_policies 
    WHERE tablename NOT IN ('users', 'readings', 'credit_transactions', 'credit_packages', 'app_configs', 'ai_prompts')
  LOOP
    RAISE NOTICE 'Found policy % on table % - review manually', policy_record.policyname, policy_record.tablename;
  END LOOP;
END $$;

-- 清理可能存在的无用索引
-- 注意：这里只是检查，不自动删除，需要手动确认
DO $$
DECLARE
  index_record RECORD;
BEGIN
  -- 查找可能的重复或无用索引
  FOR index_record IN 
    SELECT indexname, tablename 
    FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename NOT IN ('users', 'readings', 'credit_transactions', 'credit_packages', 'app_configs', 'ai_prompts')
    AND indexname NOT LIKE 'pg_%'
  LOOP
    RAISE NOTICE 'Found index % on table % - review manually', index_record.indexname, index_record.tablename;
  END LOOP;
END $$;

-- ============================================================================
-- 第二部分：优化数据库索引
-- ============================================================================

-- 为 users 表添加优化索引
CREATE INDEX IF NOT EXISTS idx_users_active_credits ON users(is_active, credits) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_users_subscription_active ON users(subscription_status, is_active) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_users_language_active ON users(language_preference, is_active) 
WHERE is_active = TRUE;

-- 为 readings 表添加复合索引
CREATE INDEX IF NOT EXISTS idx_readings_user_status_created ON readings(user_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_readings_type_status_created ON readings(type, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_readings_user_type_created ON readings(user_id, type, created_at DESC);

-- 为 credit_transactions 表添加优化索引
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_type_created ON credit_transactions(user_id, type, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_credit_transactions_status_created ON credit_transactions(status, created_at DESC) 
    WHERE status = 'completed';
    CREATE INDEX IF NOT EXISTS idx_credit_transactions_amount_type ON credit_transactions(amount, type) 
    WHERE amount > 0;
  END IF;
END $$;

-- 为 credit_packages 表添加查询优化索引
CREATE INDEX IF NOT EXISTS idx_credit_packages_active_popular ON credit_packages(is_active, is_popular, display_order) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_credit_packages_active_featured ON credit_packages(is_active, is_featured, display_order) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_credit_packages_type_active ON credit_packages(package_type, is_active, display_order) 
WHERE is_active = TRUE;

-- 为 app_configs 表添加查询优化索引
CREATE INDEX IF NOT EXISTS idx_app_configs_secret_key ON app_configs(is_secret, key) 
WHERE is_secret = FALSE;

-- 为 ai_prompts 表添加查询优化索引
CREATE INDEX IF NOT EXISTS idx_ai_prompts_active_name ON ai_prompts(is_active, name) 
WHERE is_active = TRUE;

-- ============================================================================
-- 第三部分：数据库统计和优化
-- ============================================================================

-- 更新表统计信息以优化查询计划
ANALYZE users;
ANALYZE readings;
ANALYZE credit_packages;
ANALYZE app_configs;
ANALYZE ai_prompts;

DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    ANALYZE credit_transactions;
  END IF;
END $$;

-- ============================================================================
-- 第四部分：数据一致性验证
-- ============================================================================

-- 验证外键关系
DO $$
DECLARE
  fk_violations INTEGER;
BEGIN
  -- 检查 readings 表的外键一致性
  SELECT COUNT(*) INTO fk_violations
  FROM readings r
  LEFT JOIN users u ON r.user_id = u.id
  WHERE u.id IS NULL;
  
  IF fk_violations > 0 THEN
    RAISE WARNING 'Found % orphaned readings records', fk_violations;
  ELSE
    RAISE NOTICE 'All readings records have valid user references';
  END IF;
  
  -- 检查 credit_transactions 表的外键一致性（如果存在）
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'credit_transactions') THEN
    SELECT COUNT(*) INTO fk_violations
    FROM credit_transactions ct
    LEFT JOIN users u ON ct.user_id = u.id
    WHERE u.id IS NULL;
    
    IF fk_violations > 0 THEN
      RAISE WARNING 'Found % orphaned credit_transactions records', fk_violations;
    ELSE
      RAISE NOTICE 'All credit_transactions records have valid user references';
    END IF;
  END IF;
END $$;

-- 验证数据完整性
DO $$
DECLARE
  users_count INTEGER;
  readings_count INTEGER;
  packages_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO users_count FROM users;
  SELECT COUNT(*) INTO readings_count FROM readings;
  SELECT COUNT(*) INTO packages_count FROM credit_packages WHERE is_active = TRUE;
  
  RAISE NOTICE 'Database statistics:';
  RAISE NOTICE '  Users: %', users_count;
  RAISE NOTICE '  Readings: %', readings_count;
  RAISE NOTICE '  Active credit packages: %', packages_count;
  
  -- 检查是否有必要的数据
  IF users_count = 0 THEN
    RAISE WARNING 'No users found in database';
  END IF;
  
  IF packages_count = 0 THEN
    RAISE WARNING 'No active credit packages found';
  END IF;
END $$;

-- ============================================================================
-- 第五部分：性能监控设置
-- ============================================================================

-- 创建一个视图来监控系统使用情况
CREATE OR REPLACE VIEW system_stats AS
SELECT 
  'users' as table_name,
  COUNT(*) as total_records,
  COUNT(*) FILTER (WHERE is_active = TRUE) as active_records,
  AVG(credits) as avg_credits,
  MAX(created_at) as latest_record
FROM users
UNION ALL
SELECT 
  'readings' as table_name,
  COUNT(*) as total_records,
  COUNT(*) FILTER (WHERE status = 'completed') as active_records,
  AVG(credits_consumed) as avg_credits,
  MAX(created_at) as latest_record
FROM readings
UNION ALL
SELECT 
  'credit_packages' as table_name,
  COUNT(*) as total_records,
  COUNT(*) FILTER (WHERE is_active = TRUE) as active_records,
  AVG(credits) as avg_credits,
  MAX(created_at) as latest_record
FROM credit_packages;

-- 创建一个函数来检查系统健康状态
CREATE OR REPLACE FUNCTION check_system_health()
RETURNS TABLE(
  check_name TEXT,
  status TEXT,
  details TEXT
) AS $$
BEGIN
  -- 检查用户表
  RETURN QUERY
  SELECT 
    'users_table'::TEXT,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END::TEXT,
    FORMAT('Total users: %s, Active: %s', COUNT(*), COUNT(*) FILTER (WHERE is_active = TRUE))::TEXT
  FROM users;
  
  -- 检查积分套餐
  RETURN QUERY
  SELECT 
    'credit_packages'::TEXT,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END::TEXT,
    FORMAT('Total packages: %s, Active: %s', COUNT(*), COUNT(*) FILTER (WHERE is_active = TRUE))::TEXT
  FROM credit_packages;
  
  -- 检查外键完整性
  RETURN QUERY
  SELECT 
    'foreign_key_integrity'::TEXT,
    CASE WHEN COUNT(*) = 0 THEN 'OK' ELSE 'ERROR' END::TEXT,
    FORMAT('Orphaned readings: %s', COUNT(*))::TEXT
  FROM readings r
  LEFT JOIN users u ON r.user_id = u.id
  WHERE u.id IS NULL;
  
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON VIEW system_stats IS '系统使用统计视图';
COMMENT ON FUNCTION check_system_health() IS '检查系统健康状态的函数';

-- 最终验证
SELECT * FROM check_system_health();
