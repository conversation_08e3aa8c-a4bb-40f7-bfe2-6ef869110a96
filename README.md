# Eastern Fate Master - 东方玄学大师

A modern web application that combines ancient Chinese metaphysics (BaZi/八字) with contemporary crystal healing recommendations. Built with React, TypeScript, and Tailwind CSS.

## Features

- 🔮 **BaZi Analysis**: Detailed Four Pillars destiny chart reading
- 🌟 **Five Elements Balance**: Personal elemental profile analysis  
- 💎 **Crystal Recommendations**: Personalized crystal and jade suggestions
- 🌍 **Internationalization**: Support for English and Chinese
- 🎨 **Beautiful UI**: Modern design with smooth animations
- 📱 **Responsive**: Works perfectly on all devices
- 🌙 **Dark Mode**: Toggle between light and dark themes

## Tech Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **i18next** for internationalization
- **Lucide React** for icons

### Backend
- **Supabase** for database and authentication
- **OAuth Providers** (Google, GitHub, Discord)
- **DeepSeek API** for AI-powered BaZi analysis

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd eastern-fate-master
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:
```bash
# Supabase Configuration (已配置)
VITE_SUPABASE_URL=https://tagcspajmlnnnwdrwtvf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# DeepSeek API Configuration (需要您的 API 密钥)
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here
VITE_DEEPSEEK_BASE_URL=https://api.deepseek.com
```

5. Start the development server:
```bash
npm run dev
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Header.tsx      # Navigation header
│   └── Footer.tsx      # Site footer
├── pages/              # Page components
│   ├── HomePage.tsx    # Landing page
│   ├── BirthInfoPage.tsx # Birth information form
│   ├── LoadingPage.tsx # Analysis loading screen
│   └── ResultsPage.tsx # BaZi reading results
├── lib/                # Utilities and configuration
│   ├── i18n.ts        # Internationalization setup
│   └── supabase.ts    # Supabase client
├── types/              # TypeScript type definitions
│   └── index.ts       # Shared types
└── App.tsx            # Main application component
```

## Features Overview

### 🏠 Home Page
- Beautiful hero section with animated elements
- Feature showcase with icons and descriptions
- User testimonials section
- Responsive design with dark mode support

### 📝 Birth Information Form
- Step-by-step progress indicator
- Date picker with validation
- Country and city selection
- Trust indicators and security badges
- Credit balance checking

### ⏳ Loading Experience
- Animated progress bar
- Five elements visualization
- Inspirational quotes
- Realistic timing simulation

### 📊 Results Page
- Interactive BaZi chart display
- Five elements balance visualization
- Personalized crystal recommendations
- Tabbed content for different life aspects
- Premium content unlock system
- Credit-based content access

## Internationalization

The app supports both English and Chinese languages with automatic detection based on browser settings. All text content is externalized using react-i18next.

## Deployment

### Cloudflare Pages (Recommended)
1. Build the project: `npm run build`
2. Deploy the `dist` folder to Cloudflare Pages
3. Configure environment variables in Cloudflare dashboard

### Other Platforms
The app can be deployed to any static hosting service like Netlify, Vercel, or GitHub Pages.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Ancient Chinese metaphysics and BaZi system
- Crystal healing traditions
- Modern web development community