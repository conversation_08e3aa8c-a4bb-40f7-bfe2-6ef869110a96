# 前端组件开发记录

## 组件架构设计

### 设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 组件设计考虑复用场景
- **类型安全**: 完整的 TypeScript 类型定义
- **响应式**: 所有组件支持移动端

## 核心组件详解

### 1. Header 组件
**文件**: `src/components/Header.tsx`

**功能**:
- 品牌标识展示
- 语言切换功能
- 深色模式切换
- 响应式导航

**Props**:
```typescript
interface HeaderProps {
  darkMode: boolean;
  toggleDarkMode: () => void;
}
```

**特性**:
- 固定定位，毛玻璃背景效果
- 渐变色品牌标识
- 平滑过渡动画

### 2. Footer 组件
**文件**: `src/components/Footer.tsx`

**功能**:
- 品牌信息展示
- 信任指标显示
- 社交媒体链接
- 版权信息

**特性**:
- 居中布局设计
- 图标和文字组合
- 悬停效果

### 3. HomePage 组件
**文件**: `src/pages/HomePage.tsx`

**功能**:
- 英雄区域展示
- 功能特性介绍
- 用户评价展示
- 五行元素可视化

**特性**:
- 分层动画效果
- 渐变背景设计
- 交互式元素图标
- 响应式网格布局

### 4. BirthInfoPage 组件
**文件**: `src/pages/BirthInfoPage.tsx`

**功能**:
- 出生信息表单
- 步骤进度指示
- 表单验证
- 信任指标展示

**状态管理**:
```typescript
const [formData, setFormData] = useState({
  birthDate: '',
  birthCountry: 'China',
  birthCity: ''
});
```

**特性**:
- 多步骤表单设计
- 实时验证反馈
- 本地数据存储

### 5. LoadingPage 组件
**文件**: `src/pages/LoadingPage.tsx`

**功能**:
- 分析进度展示
- 五行元素动画
- 励志文案显示
- 自动页面跳转

**动画特性**:
- 进度条动画
- 星空背景效果
- 元素轮播动画
- 脉冲效果

### 6. ResultsPage 组件
**文件**: `src/pages/ResultsPage.tsx`

**功能**:
- 八字命盘展示
- 五行平衡图表
- 水晶推荐列表
- 分类内容展示

**状态管理**:
```typescript
const [activeTab, setActiveTab] = useState('overall');
```

**特性**:
- 标签页切换
- 数据可视化
- 高级内容锁定
- 分享和下载功能

## 样式系统

### 设计令牌
```css
/* 主色调 */
--primary-gradient: linear-gradient(to right, #8b5cf6, #ec4899);
--background-gradient: linear-gradient(to bottom right, #faf5ff, #fdf2f8, #eef2ff);

/* 间距系统 */
--spacing-xs: 0.25rem;
--spacing-sm: 0.5rem;
--spacing-md: 1rem;
--spacing-lg: 1.5rem;
--spacing-xl: 2rem;

/* 圆角系统 */
--radius-sm: 0.5rem;
--radius-md: 0.75rem;
--radius-lg: 1rem;
--radius-xl: 1.5rem;
```

### 组件样式模式

#### 1. 卡片容器
```css
.card {
  @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl shadow-lg border border-white/20;
}
```

#### 2. 按钮样式
```css
.btn-primary {
  @apply bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300;
}
```

#### 3. 输入框样式
```css
.input-field {
  @apply w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300;
}
```

## 动画系统

### Framer Motion 配置

#### 页面进入动画
```typescript
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8 }
};
```

#### 交错动画
```typescript
const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.2
    }
  }
};
```

#### 悬停效果
```typescript
const hoverScale = {
  whileHover: { scale: 1.05 },
  whileTap: { scale: 0.95 }
};
```

## 响应式设计

### 断点系统
- **sm**: 640px (手机横屏)
- **md**: 768px (平板)
- **lg**: 1024px (桌面)
- **xl**: 1280px (大屏幕)

### 移动端优化
- 触摸友好的按钮大小
- 简化的导航结构
- 优化的表单布局
- 适配的字体大小

## 可访问性

### 已实现
- 语义化 HTML 结构
- 键盘导航支持
- 颜色对比度优化
- 屏幕阅读器友好

### 待改进
- ARIA 标签补充
- 焦点管理优化
- 高对比度模式

## 组件测试策略

### 单元测试
- 组件渲染测试
- Props 传递测试
- 事件处理测试

### 集成测试
- 组件交互测试
- 状态管理测试
- 路由导航测试

### 视觉回归测试
- 组件快照测试
- 跨浏览器兼容性
- 响应式布局测试