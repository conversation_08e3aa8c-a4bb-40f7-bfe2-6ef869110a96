# 后端 API 设计文档

## API 架构概述

### 基础信息
- **基础URL**: `https://api.eastern-fate-master.com`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **版本控制**: URL 路径版本 (`/v1/`)

### 通用响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}
```

### 错误代码规范
```typescript
enum ErrorCodes {
  // 认证错误 (1000-1099)
  UNAUTHORIZED = 'AUTH_001',
  TOKEN_EXPIRED = 'AUTH_002',
  INVALID_TOKEN = 'AUTH_003',
  
  // 用户错误 (1100-1199)
  USER_NOT_FOUND = 'USER_001',
  EMAIL_EXISTS = 'USER_002',
  INVALID_CREDENTIALS = 'USER_003',
  
  // 积分错误 (1200-1299)
  INSUFFICIENT_CREDITS = 'CREDIT_001',
  INVALID_TRANSACTION = 'CREDIT_002',
  
  // 业务错误 (1300-1399)
  READING_NOT_FOUND = 'READING_001',
  INVALID_BIRTH_DATA = 'READING_002',
  AI_SERVICE_ERROR = 'READING_003',
  
  // 系统错误 (9000-9999)
  INTERNAL_ERROR = 'SYS_001',
  SERVICE_UNAVAILABLE = 'SYS_002',
  RATE_LIMIT_EXCEEDED = 'SYS_003'
}
```

## 认证 API

### 用户注册
```http
POST /v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "张三",
  "language": "zh"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "张三",
      "credits": 1,
      "language_preference": "zh"
    },
    "token": "jwt_token_here",
    "expires_in": 3600
  }
}
```

### 用户登录
```http
POST /v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### 获取用户信息
```http
GET /v1/auth/profile
Authorization: Bearer {token}
```

### 更新用户信息
```http
PUT /v1/auth/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新名字",
  "language_preference": "en",
  "timezone": "Asia/Shanghai"
}
```

## 八字解读 API

### 创建解读
```http
POST /v1/readings
Authorization: Bearer {token}
Content-Type: application/json

{
  "birth_date": "1987-02-01",
  "birth_time": "14:30:00",
  "birth_location": {
    "country": "China",
    "city": "Beijing",
    "latitude": 39.9042,
    "longitude": 116.4074
  },
  "timezone": "Asia/Shanghai",
  "gender": "male",
  "language": "zh",
  "type": "basic"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "reading_uuid",
    "status": "processing",
    "estimated_completion": "2025-01-27T10:35:00Z",
    "credits_consumed": 10
  }
}
```

### 获取解读结果
```http
GET /v1/readings/{reading_id}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "reading_uuid",
    "status": "completed",
    "type": "basic",
    "birth_info": {
      "date": "1987-02-01",
      "time": "14:30:00",
      "location": "Beijing, China"
    },
    "bazi_chart": {
      "year": { "heavenly": "丁", "earthly": "卯", "element": "Fire" },
      "month": { "heavenly": "壬", "earthly": "寅", "element": "Water" },
      "day": { "heavenly": "己", "earthly": "丑", "element": "Earth" },
      "hour": { "heavenly": "甲", "earthly": "子", "element": "Wood" }
    },
    "five_elements": {
      "wood": 25,
      "fire": 30,
      "earth": 20,
      "metal": 15,
      "water": 10
    },
    "readings": {
      "overall": "整体运势解读内容...",
      "career": "事业财运解读内容...",
      "relationships": "感情姻缘解读内容...",
      "health": "健康养生解读内容..."
    },
    "crystal_recommendations": [
      {
        "name": "Rose Quartz",
        "name_zh": "粉水晶",
        "element": "Earth",
        "benefit": "增强爱情和情感疗愈",
        "usage": "佩戴或放置在卧室"
      }
    ],
    "confidence_score": 0.85,
    "created_at": "2025-01-27T10:30:00Z"
  }
}
```

### 获取用户解读历史
```http
GET /v1/readings?page=1&limit=10&type=basic
Authorization: Bearer {token}
```

## 积分系统 API

### 获取积分余额
```http
GET /v1/credits/balance
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "current_balance": 25,
    "total_earned": 50,
    "total_spent": 25,
    "pending_transactions": 0
  }
}
```

### 消费积分
```http
POST /v1/credits/consume
Authorization: Bearer {token}
Content-Type: application/json

{
  "amount": 10,
  "type": "reading",
  "reference_id": "reading_uuid",
  "description": "基础八字解读"
}
```

### 获取积分交易历史
```http
GET /v1/credits/transactions?page=1&limit=20&type=all
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "transaction_uuid",
        "type": "consume",
        "amount": -10,
        "balance_before": 35,
        "balance_after": 25,
        "description": "基础八字解读",
        "reference_id": "reading_uuid",
        "created_at": "2025-01-27T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

### 购买积分
```http
POST /v1/credits/purchase
Authorization: Bearer {token}
Content-Type: application/json

{
  "package_id": "package_uuid",
  "payment_method": "stripe",
  "return_url": "https://app.com/payment/success"
}
```

## 分享功能 API

### 创建分享链接
```http
POST /v1/shares
Authorization: Bearer {token}
Content-Type: application/json

{
  "reading_id": "reading_uuid",
  "share_type": "public",
  "expires_at": "2025-02-27T10:30:00Z",
  "max_views": 100,
  "allow_comments": false,
  "show_personal_info": false,
  "custom_message": "分享我的八字解读"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "share_uuid",
    "share_token": "abc123def456",
    "share_url": "https://app.com/share/abc123def456",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expires_at": "2025-02-27T10:30:00Z"
  }
}
```

### 访问分享内容
```http
GET /v1/shares/{share_token}
```

### 更新分享设置
```http
PUT /v1/shares/{share_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "max_views": 200,
  "allow_comments": true
}
```

### 删除分享
```http
DELETE /v1/shares/{share_id}
Authorization: Bearer {token}
```

## 邀请系统 API

### 生成邀请码
```http
POST /v1/invitations/generate
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "invitation_code": "INVITE123",
    "invitation_url": "https://app.com/register?invite=INVITE123",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "reward_credits": 30
  }
}
```

### 使用邀请码注册
```http
POST /v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "新用户",
  "invitation_code": "INVITE123"
}
```

### 获取邀请统计
```http
GET /v1/invitations/stats
Authorization: Bearer {token}
```

## 管理员 API

### 用户管理
```http
GET /v1/admin/users?page=1&limit=50&search=email
POST /v1/admin/users/{user_id}/credits
PUT /v1/admin/users/{user_id}/status
```

### 系统统计
```http
GET /v1/admin/stats/overview
GET /v1/admin/stats/readings
GET /v1/admin/stats/revenue
```

### 内容管理
```http
GET /v1/admin/readings?status=pending
PUT /v1/admin/readings/{reading_id}/review
DELETE /v1/admin/readings/{reading_id}
```

## Webhook API

### 支付回调
```http
POST /v1/webhooks/payment/stripe
Content-Type: application/json
Stripe-Signature: signature_here

{
  "type": "payment_intent.succeeded",
  "data": {
    "object": {
      "id": "pi_1234567890",
      "amount": 2000,
      "metadata": {
        "user_id": "user_uuid",
        "package_id": "package_uuid"
      }
    }
  }
}
```

## 限流策略

### 认证端点
- 登录: 5 次/分钟/IP
- 注册: 3 次/分钟/IP
- 密码重置: 3 次/小时/邮箱

### 业务端点
- 创建解读: 10 次/小时/用户
- 获取解读: 100 次/小时/用户
- 分享创建: 20 次/小时/用户

### 公共端点
- 分享访问: 1000 次/小时/IP
- 健康检查: 无限制

## 缓存策略

### Redis 缓存
```typescript
// 用户信息缓存 (30分钟)
const userCacheKey = `user:${userId}`;

// 解读结果缓存 (24小时)
const readingCacheKey = `reading:${readingId}`;

// 积分余额缓存 (5分钟)
const creditsCacheKey = `credits:${userId}`;

// 分享内容缓存 (1小时)
const shareCacheKey = `share:${shareToken}`;
```

### CDN 缓存
- 静态资源: 1年
- API 响应: 不缓存
- 分享页面: 1小时

## 监控和日志

### 关键指标
- API 响应时间
- 错误率统计
- 用户活跃度
- 积分消费情况
- AI 服务调用成功率

### 日志格式
```json
{
  "timestamp": "2025-01-27T10:30:00Z",
  "level": "INFO",
  "service": "api",
  "endpoint": "/v1/readings",
  "method": "POST",
  "user_id": "user_uuid",
  "request_id": "req_uuid",
  "duration_ms": 250,
  "status_code": 200,
  "message": "Reading created successfully"
}
```