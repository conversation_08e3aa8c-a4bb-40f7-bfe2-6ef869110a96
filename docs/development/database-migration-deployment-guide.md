# 数据库Migration部署指南

## 📋 部署概述

本指南用于部署数据库与代码一致性修复的所有migration文件。

**修复日期**: 2025-01-10  
**Migration文件数量**: 6个  
**预计执行时间**: 10-15分钟  

## 🚨 部署前准备

### 1. 数据备份
```sql
-- 备份现有数据（如果有重要数据）
pg_dump -h your-host -U your-user -d your-database > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 检查当前数据库状态
```sql
-- 检查现有表
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- 检查是否有数据需要迁移
SELECT 'readings' as table_name, COUNT(*) as record_count FROM readings
UNION ALL
SELECT 'credits', COUNT(*) FROM credits WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'credits');
```

## 📝 Migration文件执行顺序

**⚠️ 重要**: 必须按照以下顺序执行migration文件

### 1. 创建用户表
```bash
# 文件: supabase/migrations/20250110000001_create_users_table.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000001_create_users_table.sql
```

**预期结果**:
- ✅ 创建 `users` 表
- ✅ 创建相关索引和约束
- ✅ 设置 RLS 策略
- ✅ 插入测试用户数据

### 2. 创建积分套餐表
```bash
# 文件: supabase/migrations/20250110000002_create_credit_packages_table.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000002_create_credit_packages_table.sql
```

**预期结果**:
- ✅ 创建 `credit_packages` 表
- ✅ 插入默认套餐数据
- ✅ 设置 RLS 策略

### 3. 重构解读表
```bash
# 文件: supabase/migrations/20250110000003_rebuild_readings_table.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000003_rebuild_readings_table.sql
```

**预期结果**:
- ✅ 备份现有 `readings` 表数据（如果有）
- ✅ 删除旧的 `readings` 表
- ✅ 创建新的 `readings` 表结构
- ✅ 插入测试数据

### 4. 统一积分系统
```bash
# 文件: supabase/migrations/20250110000004_unify_credit_system.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000004_unify_credit_system.sql
```

**预期结果**:
- ✅ 迁移 `credits` 表数据到 `users.credits`
- ✅ 删除独立的 `credits` 表
- ✅ 更新相关 RLS 策略

### 5. 修复外键关系
```bash
# 文件: supabase/migrations/20250110000005_fix_foreign_key_relationships.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000005_fix_foreign_key_relationships.sql
```

**预期结果**:
- ✅ 修复所有外键约束
- ✅ 更新 RLS 策略
- ✅ 创建辅助函数

### 6. 清理和优化
```bash
# 文件: supabase/migrations/20250110000006_cleanup_and_optimize.sql
psql -h your-host -U your-user -d your-database -f supabase/migrations/20250110000006_cleanup_and_optimize.sql
```

**预期结果**:
- ✅ 优化数据库索引
- ✅ 清理冗余对象
- ✅ 创建监控视图和函数

## 🧪 部署后验证

### 1. 检查表结构
```sql
-- 验证所有必要的表都存在
SELECT table_name, 
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'readings', 'credit_packages', 'credit_transactions', 'app_configs', 'ai_prompts')
ORDER BY table_name;
```

### 2. 检查外键关系
```sql
-- 验证外键约束
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public';
```

### 3. 检查系统健康状态
```sql
-- 使用新创建的健康检查函数
SELECT * FROM check_system_health();
```

### 4. 验证数据完整性
```sql
-- 检查测试数据
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'credit_packages', COUNT(*) FROM credit_packages WHERE is_active = TRUE
UNION ALL
SELECT 'readings', COUNT(*) FROM readings;
```

## 🔧 故障排除

### 常见问题

#### 1. Migration执行失败
```sql
-- 检查错误日志
SELECT * FROM pg_stat_activity WHERE state = 'active';

-- 回滚到备份（如果需要）
-- 恢复备份文件
```

#### 2. 外键约束错误
```sql
-- 检查孤立记录
SELECT r.id, r.user_id 
FROM readings r 
LEFT JOIN users u ON r.user_id = u.id 
WHERE u.id IS NULL;
```

#### 3. RLS策略问题
```sql
-- 检查 RLS 策略
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

### 回滚计划

如果需要回滚，按照以下步骤：

1. **停止应用服务**
2. **恢复数据库备份**
```bash
psql -h your-host -U your-user -d your-database < backup_file.sql
```
3. **验证回滚成功**
4. **重启应用服务**

## 📊 性能验证

### 查询性能测试
```sql
-- 测试用户查询性能
EXPLAIN ANALYZE SELECT * FROM users WHERE clerk_user_id = 'test-user';

-- 测试解读查询性能
EXPLAIN ANALYZE SELECT * FROM readings WHERE user_id = (SELECT id FROM users WHERE clerk_user_id = 'test-user');

-- 测试积分套餐查询性能
EXPLAIN ANALYZE SELECT * FROM credit_packages WHERE is_active = TRUE ORDER BY display_order;
```

## ✅ 部署检查清单

- [ ] 数据库备份完成
- [ ] Migration文件按顺序执行
- [ ] 所有表创建成功
- [ ] 外键关系正确
- [ ] RLS策略生效
- [ ] 测试数据插入成功
- [ ] 系统健康检查通过
- [ ] 查询性能正常
- [ ] 应用功能测试通过

## 📞 支持联系

如果在部署过程中遇到问题，请：

1. 检查错误日志
2. 参考故障排除部分
3. 保留错误信息和日志
4. 联系开发团队

---

**创建时间**: 2025-01-10  
**适用版本**: 数据库代码一致性修复 v1.0  
**维护人员**: Augment Agent
