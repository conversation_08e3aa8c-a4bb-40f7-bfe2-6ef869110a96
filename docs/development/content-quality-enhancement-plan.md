# 内容质量提升与功能完善开发计划

## 📋 项目概述

基于用户反馈和原型图分析，当前平台的分析内容质量和展示效果需要大幅提升。本计划旨在将平台从基础功能提升到专业级八字命理分析平台。

## 🎯 核心目标

1. **提升AI分析内容质量**：从简单描述到专业深度分析
2. **优化视觉展示效果**：专业的八字命盘和分析结果展示
3. **完善邀请积分系统**：支持病毒式增长的核心功能
4. **增强用户体验**：流畅的使用流程和丰富的功能

## 📊 当前问题分析

### 内容质量问题
- AI 生成内容过于简单，缺乏深度
- 分析维度有限，不够全面
- 缺乏专业命理术语和概念
- 内容格式单一，可读性差

### 展示效果问题
- 八字命盘展示过于简陋
- 缺乏五行分析的可视化
- 结果页面布局不够专业
- 缺乏层次感和视觉吸引力

### 功能缺失问题
- 没有邀请积分系统
- 缺乏用户历史记录
- 没有社交分享功能
- 用户体验流程不完整

## 🚀 开发阶段规划

### Phase 1: 内容质量提升 (优先级: 🔥🔥🔥)

#### 1.1 AI 提示词优化
- **目标**: 让 DeepSeek 生成专业、详细的命理分析
- **工作内容**:
  - 重新设计 AI 提示词模板
  - 增加专业命理术语词汇库
  - 优化分析结构和逻辑
  - 增加具体建议和指导内容

#### 1.2 分析维度扩展
- **目标**: 提供全方位的人生分析
- **工作内容**:
  - 事业运势深度分析
  - 感情婚姻运势解读
  - 财运分析和建议
  - 健康运势和养生建议
  - 性格特点和人际关系
  - 学业发展和职业规划

#### 1.3 内容结构优化
- **目标**: 提升内容的可读性和专业性
- **工作内容**:
  - 模块化内容结构设计
  - 增加小标题和段落划分
  - 添加重点内容高亮
  - 优化内容长度和深度

### Phase 2: 视觉展示优化 (优先级: 🔥🔥)

#### 2.1 八字命盘可视化
- **目标**: 专业的传统八字展示
- **工作内容**:
  - 设计传统四柱八字布局
  - 添加天干地支的专业展示
  - 五行属性颜色标识
  - 命盘解读说明

#### 2.2 五行分析图表
- **目标**: 直观的五行平衡展示
- **工作内容**:
  - 五行雷达图或柱状图
  - 五行强弱分析
  - 五行相生相克关系图
  - 平衡建议可视化

#### 2.3 结果页面重构
- **目标**: 专业且吸引人的展示效果
- **工作内容**:
  - 重新设计页面布局
  - 优化色彩搭配和字体
  - 增加动画和交互效果
  - 提升整体视觉层次

### Phase 3: 邀请积分系统 (优先级: 🔥)

#### 3.1 邀请码系统
- **目标**: 支持用户邀请获取积分
- **工作内容**:
  - 生成唯一邀请码
  - 邀请链接分享功能
  - 邀请成功检测
  - 积分奖励发放

#### 3.2 积分管理优化
- **目标**: 完善的积分使用体验
- **工作内容**:
  - 积分获取历史记录
  - 积分使用明细
  - 积分余额实时更新
  - 积分规则说明

#### 3.3 社交分享功能
- **目标**: 便于用户分享和传播
- **工作内容**:
  - 分析结果分享
  - 邀请链接分享
  - 社交媒体集成
  - 分享统计追踪

## 📅 开发时间线

### 第1周: Phase 1 - 内容质量提升
- Day 1-2: AI 提示词优化和测试
- Day 3-4: 分析维度扩展实现
- Day 5-7: 内容结构优化和测试

### 第2周: Phase 2 - 视觉展示优化
- Day 1-3: 八字命盘可视化开发
- Day 4-5: 五行分析图表实现
- Day 6-7: 结果页面重构和优化

### 第3周: Phase 3 - 邀请积分系统
- Day 1-3: 邀请码系统开发
- Day 4-5: 积分管理功能完善
- Day 6-7: 社交分享功能实现

## 🎯 成功指标

### 内容质量指标
- 分析内容字数: 从当前 < 500字 提升到 > 2000字
- 分析维度: 从 3个 扩展到 8个
- 用户满意度: 目标 > 85%

### 用户体验指标
- 页面停留时间: 提升 > 200%
- 分享转化率: 目标 > 15%
- 邀请成功率: 目标 > 10%

### 技术指标
- 页面加载速度: < 3秒
- API 响应时间: < 2秒
- 系统稳定性: > 99.5%

## 📝 开发记录

### 2024-01-09

#### Phase 1: 内容质量提升 - AI 提示词优化 ✅ 完成
- ✅ 创建开发计划文档
- ✅ 重新设计 AI 提示词模板，增加专业命理术语
- ✅ 提升系统提示词的专业性和权威性
- ✅ 扩展分析维度：事业、感情、健康、财运、发展建议
- ✅ 增加内容深度要求，每个模块至少250-300字
- ✅ 优化水晶推荐的专业性和针对性
- ✅ 提高token限制到4000以支持更详细内容
- ✅ 测试验证：分析时间从126秒增加到212秒，内容质量显著提升

**成果**：
- AI 分析内容专业性大幅提升
- 分析深度和广度显著增强
- 水晶推荐更加精准和专业
- 为后续视觉优化奠定了内容基础

#### Phase 2: 视觉展示优化 - 八字命盘和五行分析 ✅ 部分完成
- ✅ 创建专业的BaZiChart组件
  - 传统四柱八字布局（年月日时）
  - 五行颜色标识和渐变效果
  - 天干地支分层展示
  - 响应式设计和动画效果
- ✅ 创建ElementProfile组件
  - 五行雷达图可视化
  - 详细的五行进度条和强弱分析
  - 五行属性说明和健康关联
  - 专业的颜色系统和图标
- ✅ 更新ResultsPage使用新的可视化组件

**成果**：
- 八字命盘展示更加专业和传统
- 五行分析可视化效果显著提升
- 整体视觉层次和专业性大幅改善

#### 下一步：Phase 2 继续 - 结果页面重构
- 🔄 优化分析内容的展示格式
- 🔄 增加模块化的内容布局

## 🔧 Prompt 数据库化整改方案

### 数据库表结构设计

#### 1. ai_prompts 表
```sql
CREATE TABLE ai_prompts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,           -- prompt名称标识
  category VARCHAR(50) NOT NULL,               -- 分类：destiny_analysis, readings, crystal_recommendations
  version INTEGER NOT NULL DEFAULT 1,          -- 版本号
  title VARCHAR(200) NOT NULL,                 -- 显示标题
  system_prompt TEXT NOT NULL,                 -- 系统提示词
  user_prompt_template TEXT NOT NULL,          -- 用户提示词模板
  variables JSONB,                             -- 模板变量定义
  is_active BOOLEAN DEFAULT true,              -- 是否启用
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),

  UNIQUE(name, version)
);
```

#### 2. prompt_versions 表（版本历史）
```sql
CREATE TABLE prompt_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  prompt_id UUID REFERENCES ai_prompts(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  change_log TEXT,                             -- 变更说明
  performance_metrics JSONB,                   -- 性能指标
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);
```

#### 3. prompt_usage_logs 表（使用记录）
```sql
CREATE TABLE prompt_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  prompt_id UUID REFERENCES ai_prompts(id),
  reading_id UUID REFERENCES readings(id),
  execution_time_ms INTEGER,                   -- 执行时间
  token_count INTEGER,                         -- token消耗
  success BOOLEAN,                             -- 是否成功
  error_message TEXT,                          -- 错误信息
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 服务层重构

#### 1. PromptService 类
```typescript
// workers/src/services/prompt.ts
export class PromptService {
  private supabase: SupabaseClient;
  private cache: Map<string, CachedPrompt> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

  async getPrompt(name: string, variables?: Record<string, any>): Promise<ProcessedPrompt> {
    // 1. 检查缓存
    const cached = this.getCachedPrompt(name);
    if (cached) return this.processPrompt(cached, variables);

    // 2. 从数据库获取
    const prompt = await this.fetchPromptFromDB(name);
    if (!prompt) throw new Error(`Prompt not found: ${name}`);

    // 3. 缓存并返回
    this.cachePrompt(name, prompt);
    return this.processPrompt(prompt, variables);
  }

  private processPrompt(prompt: AIPrompt, variables?: Record<string, any>): ProcessedPrompt {
    let userPrompt = prompt.user_prompt_template;

    // 替换模板变量
    if (variables) {
      Object.entries(variables).forEach(([key, value]) => {
        userPrompt = userPrompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      });
    }

    return {
      systemPrompt: prompt.system_prompt,
      userPrompt,
      metadata: {
        id: prompt.id,
        name: prompt.name,
        version: prompt.version
      }
    };
  }

  async logUsage(promptId: string, readingId: string, metrics: UsageMetrics): Promise<void> {
    await this.supabase.from('prompt_usage_logs').insert({
      prompt_id: promptId,
      reading_id: readingId,
      execution_time_ms: metrics.executionTime,
      token_count: metrics.tokenCount,
      success: metrics.success,
      error_message: metrics.errorMessage
    });
  }
}
```

#### 2. DeepSeekService 重构
```typescript
// workers/src/services/deepseek.ts
export class DeepSeekService {
  private promptService: PromptService;

  async analyzeDestiny(chart: BaZiChart, elementProfile: ElementProfile): Promise<DestinyAnalysis> {
    const startTime = Date.now();

    try {
      // 获取命理分析prompt
      const prompt = await this.promptService.getPrompt('destiny_analysis', {
        year_pillar: `${chart.year.heavenly}${chart.year.earthly}`,
        month_pillar: `${chart.month.heavenly}${chart.month.earthly}`,
        day_pillar: `${chart.day.heavenly}${chart.day.earthly}`,
        hour_pillar: `${chart.hour.heavenly}${chart.hour.earthly}`,
        wood_percent: elementProfile.wood,
        fire_percent: elementProfile.fire,
        earth_percent: elementProfile.earth,
        metal_percent: elementProfile.metal,
        water_percent: elementProfile.water
      });

      const response = await this.makeRequest(prompt.systemPrompt, prompt.userPrompt);

      // 记录使用日志
      await this.promptService.logUsage(prompt.metadata.id, '', {
        executionTime: Date.now() - startTime,
        tokenCount: this.estimateTokens(response),
        success: true
      });

      return this.parseDestinyAnalysis(response);
    } catch (error) {
      // 记录错误日志
      await this.promptService.logUsage(prompt.metadata.id, '', {
        executionTime: Date.now() - startTime,
        success: false,
        errorMessage: error.message
      });
      throw error;
    }
  }
}
```

### 初始化数据脚本

#### 1. 创建初始 Prompt 数据
```sql
-- 命理分析 prompt
INSERT INTO ai_prompts (name, category, title, system_prompt, user_prompt_template, variables) VALUES
(
  'destiny_analysis',
  'destiny_analysis',
  '八字命理分析',
  '你是一位德高望重的八字命理宗师，拥有40年的实战经验，精通《滴天髓》《穷通宝鉴》《子平真诠》等经典命理著作。你擅长运用传统命理理论结合现代生活实际，为求测者提供深度、专业、实用的命理分析和人生指导。请用专业术语和深厚的理论功底，为用户提供高质量的命理解读。',
  '请分析以下八字命盘：

年柱：{{year_pillar}}（{{year_element}}）
月柱：{{month_pillar}}（{{month_element}}）
日柱：{{day_pillar}}（{{day_element}}）
时柱：{{hour_pillar}}（{{hour_element}}）

五行分布：
木：{{wood_percent}}%
火：{{fire_percent}}%
土：{{earth_percent}}%
金：{{metal_percent}}%
水：{{water_percent}}%

请提供深度专业的命理分析，包含：
1. 整体命运格局分析（300字以上）
2. 五行平衡与调候分析（250字以上）
3. 性格特质与天赋分析（250字以上）
4. 人生优势与潜在挑战（250字以上）
5. 具体的人生建议与发展方向（300字以上）

请以JSON格式返回，确保内容专业、详细、实用：
{
  "overall": "整体命运格局分析内容",
  "elements": "五行平衡分析内容",
  "personality": "性格特质分析内容",
  "strengths": "优势分析内容",
  "challenges": "挑战分析内容",
  "advice": "人生建议内容"
}',
  '{"year_pillar": "年柱", "month_pillar": "月柱", "day_pillar": "日柱", "hour_pillar": "时柱", "year_element": "年柱五行", "month_element": "月柱五行", "day_element": "日柱五行", "hour_element": "时柱五行", "wood_percent": "木的百分比", "fire_percent": "火的百分比", "earth_percent": "土的百分比", "metal_percent": "金的百分比", "water_percent": "水的百分比"}'::jsonb
);

-- 详细解读 prompt
INSERT INTO ai_prompts (name, category, title, system_prompt, user_prompt_template, variables) VALUES
(
  'detailed_readings',
  'readings',
  '人生各方面详细解读',
  '你是一位经验丰富的命理咨询师，擅长将八字命理分析转化为具体的人生指导建议。你的解读既要保持传统命理的专业性，又要贴近现代人的生活实际，提供可操作的建议。',
  '基于以下八字命理分析结果，请提供各个人生领域的详细解读：

基础分析：{{base_analysis}}

请针对以下五个方面提供深入解读（每个方面300字以上）：

1. 事业发展运势：包括适合的行业、职业发展建议、创业时机等
2. 感情婚姻运势：包括感情模式、婚姻时机、伴侣特质等
3. 健康养生建议：包括体质特点、易患疾病、养生方法等
4. 财运理财分析：包括财运特点、理财建议、投资方向等
5. 个人发展建议：包括学习方向、技能提升、人际关系等

请以JSON格式返回：
{
  "career": "事业发展详细解读",
  "relationships": "感情婚姻详细解读",
  "health": "健康养生详细建议",
  "wealth": "财运理财详细分析",
  "development": "个人发展详细建议"
}',
  '{"base_analysis": "基础八字分析结果"}'::jsonb
);

-- 水晶推荐 prompt
INSERT INTO ai_prompts (name, category, title, system_prompt, user_prompt_template, variables) VALUES
(
  'crystal_recommendations',
  'crystal_recommendations',
  '水晶疗愈推荐',
  '你是一位精通水晶疗愈和五行理论的专家，能够根据八字五行分析为用户推荐最适合的水晶和玉石。你的推荐既要符合传统五行理论，又要考虑现代水晶疗愈的实用性。',
  '根据以下五行分析结果，请推荐适合的水晶和玉石：

五行分布：
木：{{wood_percent}}%（{{wood_status}}）
火：{{fire_percent}}%（{{fire_status}}）
土：{{earth_percent}}%（{{earth_status}}）
金：{{metal_percent}}%（{{metal_status}}）
水：{{water_percent}}%（{{water_status}}）

请推荐3-5种最适合的水晶，每种水晶包含：
- 水晶名称和颜色
- 对应的五行属性
- 具体的疗愈功效
- 佩戴建议和注意事项
- 与用户五行的匹配原因

请以JSON格式返回：
{
  "primary": {
    "name": "主推水晶名称",
    "color": "颜色",
    "element": "五行属性",
    "benefits": "主要功效",
    "usage": "使用建议",
    "reason": "推荐原因"
  },
  "secondary": [
    {
      "name": "辅助水晶名称",
      "color": "颜色",
      "element": "五行属性",
      "benefits": "主要功效",
      "usage": "使用建议",
      "reason": "推荐原因"
    }
  ]
}',
  '{"wood_percent": "木百分比", "fire_percent": "火百分比", "earth_percent": "土百分比", "metal_percent": "金百分比", "water_percent": "水百分比", "wood_status": "木的状态", "fire_status": "火的状态", "earth_status": "土的状态", "metal_status": "金的状态", "water_status": "水的状态"}'::jsonb
);
```

### 管理界面设计

#### 1. Prompt 管理 API
```typescript
// workers/src/routes/admin/prompts.ts
export const promptsRouter = new Hono();

// 获取所有 prompts
promptsRouter.get('/', async (c) => {
  const prompts = await promptService.getAllPrompts();
  return c.json({ success: true, data: prompts });
});

// 创建新 prompt
promptsRouter.post('/', async (c) => {
  const promptData = await c.req.json();
  const prompt = await promptService.createPrompt(promptData);
  return c.json({ success: true, data: prompt });
});

// 更新 prompt
promptsRouter.put('/:id', async (c) => {
  const id = c.req.param('id');
  const updates = await c.req.json();
  const prompt = await promptService.updatePrompt(id, updates);
  return c.json({ success: true, data: prompt });
});

// 获取使用统计
promptsRouter.get('/:id/stats', async (c) => {
  const id = c.req.param('id');
  const stats = await promptService.getUsageStats(id);
  return c.json({ success: true, data: stats });
});
```

### 实施步骤

#### 阶段一：数据库结构（1天）
1. 创建 ai_prompts 相关表
2. 编写初始化数据脚本
3. 设置 RLS 安全策略

#### 阶段二：服务层重构（2天）
1. 实现 PromptService 类
2. 重构 DeepSeekService
3. 添加缓存和日志功能

#### 阶段三：数据迁移（1天）
1. 将现有 prompt 迁移到数据库
2. 测试新的 prompt 系统
3. 验证功能完整性

#### 阶段四：管理功能（2天）
1. 实现 prompt 管理 API
2. 创建简单的管理界面
3. 添加版本控制功能

### 优势总结

✅ **安全性提升**：敏感 prompt 不再硬编码
✅ **动态管理**：可在线修改和优化 prompt
✅ **版本控制**：支持 prompt 版本管理和回滚
✅ **性能监控**：详细的使用统计和性能分析
✅ **A/B测试**：支持多版本 prompt 对比测试
✅ **团队协作**：多人可协作优化 prompt 内容

---

*本文档将持续更新开发进度和成果记录*
