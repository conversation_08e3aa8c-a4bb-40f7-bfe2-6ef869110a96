// 测试认证流程的脚本
const API_BASE = 'http://localhost:8787';

async function testUserRegistration() {
  console.log('🧪 测试用户注册...');
  
  const userData = {
    clerk_user_id: `test-user-${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    name: 'Test User',
    credits: 10,
    language_preference: 'zh'
  };
  
  try {
    const response = await fetch(`${API_BASE}/v1/users/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });
    
    const result = await response.json();
    console.log('✅ 注册结果:', response.status, result);
    
    if (response.ok) {
      return userData;
    } else {
      throw new Error(`Registration failed: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ 注册失败:', error);
    return null;
  }
}

async function testUserProfile(userId) {
  console.log('🧪 测试用户信息获取...');
  
  try {
    // 使用开发环境的测试token
    const response = await fetch(`${API_BASE}/v1/users/profile`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer dev-user-123'
      }
    });
    
    const result = await response.json();
    console.log('✅ 用户信息结果:', response.status, result);
    
    return response.ok;
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error);
    return false;
  }
}

async function testCreditPackages() {
  console.log('🧪 测试积分套餐获取...');
  
  try {
    const response = await fetch(`${API_BASE}/v1/credits/packages`);
    const result = await response.json();
    console.log('✅ 积分套餐结果:', response.status, result);
    
    return response.ok;
  } catch (error) {
    console.error('❌ 获取积分套餐失败:', error);
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始测试认证和API功能...\n');
  
  // 测试用户注册
  const userData = await testUserRegistration();
  console.log('');
  
  // 测试用户信息获取
  await testUserProfile(userData?.clerk_user_id);
  console.log('');
  
  // 测试积分套餐
  await testCreditPackages();
  console.log('');
  
  console.log('🏁 测试完成');
}

// 运行测试
runTests().catch(console.error);
